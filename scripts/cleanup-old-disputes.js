// Clean up old disputes from benefit_verifications table after migration
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function cleanupOldDisputes() {
  const client = await pool.connect()
  
  try {
    console.log('🧹 Cleaning Up Old Disputes')
    console.log('=' .repeat(40))
    
    await client.query('BEGIN')
    
    // 1. Check what disputes exist before cleanup
    console.log('\n1. Checking old disputes before cleanup...')
    const oldDisputes = await client.query(`
      SELECT 
        bv.id,
        bv.comment,
        b.name as benefit_name,
        c.name as company_name
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE bv.status = 'disputed'
    `)
    
    console.log(`   Found ${oldDisputes.rows.length} old disputes:`)
    oldDisputes.rows.forEach(dispute => {
      console.log(`   - ${dispute.benefit_name} at ${dispute.company_name}`)
    })
    
    // 2. Verify migration was successful
    console.log('\n2. Verifying migration to new system...')
    const newDisputes = await client.query('SELECT COUNT(*) as count FROM benefit_removal_disputes')
    console.log(`   New dispute system has ${newDisputes.rows[0].count} disputes`)
    
    if (parseInt(newDisputes.rows[0].count) >= oldDisputes.rows.length) {
      console.log('   ✅ Migration appears successful')
      
      // 3. Remove old disputes
      console.log('\n3. Removing old disputes from benefit_verifications...')
      const deleteResult = await client.query(`
        DELETE FROM benefit_verifications WHERE status = 'disputed'
      `)
      
      console.log(`   ✅ Deleted ${deleteResult.rowCount} old disputes`)
      
      await client.query('COMMIT')
      
      console.log('\n' + '=' .repeat(40))
      console.log('✅ Cleanup completed successfully!')
      console.log(`   • Old disputes removed: ${deleteResult.rowCount}`)
      console.log(`   • New disputes available: ${newDisputes.rows[0].count}`)
      
    } else {
      await client.query('ROLLBACK')
      console.log('\n❌ Migration verification failed!')
      console.log('   Not enough disputes in new system. Cleanup aborted.')
    }
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Cleanup failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

cleanupOldDisputes().catch(console.error)
