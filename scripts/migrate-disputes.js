// Migration script to move old disputes to new system
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function migrateDisputes() {
  const client = await pool.connect()
  
  try {
    console.log('🔄 Migrating Old Disputes to New System')
    console.log('=' .repeat(50))
    
    await client.query('BEGIN')
    
    // 1. Get all disputed verifications
    console.log('\n1. Fetching disputed verifications...')
    const disputes = await client.query(`
      SELECT 
        bv.id as verification_id,
        bv.company_benefit_id,
        bv.user_id,
        bv.status,
        bv.comment,
        bv.created_at,
        cb.is_verified as benefit_is_verified,
        b.name as benefit_name,
        c.name as company_name
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE bv.status = 'disputed'
      ORDER BY bv.created_at ASC
    `)
    
    console.log(`   Found ${disputes.rows.length} disputed verifications`)
    
    let migratedCount = 0
    let skippedCount = 0
    
    // 2. Process each dispute
    for (const dispute of disputes.rows) {
      console.log(`\n   Processing: ${dispute.benefit_name} at ${dispute.company_name}`)
      
      // Migrate ALL disputes to removal disputes, regardless of verification status
      console.log('     → Migrating to removal dispute system')

      // Check if removal dispute already exists
      const existingRemovalDispute = await client.query(`
        SELECT id FROM benefit_removal_disputes
        WHERE company_benefit_id = $1 AND user_id = $2
      `, [dispute.company_benefit_id, dispute.user_id])

      if (existingRemovalDispute.rows.length === 0) {
        // Create removal dispute
        let reason = dispute.comment || 'Migrated from verification dispute'

        if (dispute.benefit_is_verified) {
          reason = dispute.comment || 'Migrated from verification dispute - user disputed this verified benefit'
        } else {
          reason = dispute.comment || 'Migrated from verification dispute - user disputed the existence of this benefit'
        }

        await client.query(`
          INSERT INTO benefit_removal_disputes (
            company_benefit_id,
            user_id,
            reason,
            status,
            created_at,
            updated_at
          ) VALUES ($1, $2, $3, 'pending', $4, $4)
        `, [
          dispute.company_benefit_id,
          dispute.user_id,
          reason,
          dispute.created_at
        ])

        console.log('     ✅ Created removal dispute')
        migratedCount++
      } else {
        console.log('     ⚠️  Removal dispute already exists, skipping')
        skippedCount++
      }
    }
    
    // 3. Migration completed
    console.log('\n2. Migration processing completed')
    
    await client.query('COMMIT')
    
    console.log('\n' + '=' .repeat(50))
    console.log('✅ Migration completed successfully!')
    console.log(`   • Total disputes found: ${disputes.rows.length}`)
    console.log(`   • Migrated to removal disputes: ${migratedCount}`)
    console.log(`   • Skipped (unverified benefits): ${skippedCount}`)
    
    if (migratedCount > 0) {
      console.log('\n💡 Note: All disputes have been migrated to the new removal dispute system.')
      console.log('   The new system handles both verified and unverified benefit disputes.')
      console.log('   Users can now request removal through the democratic process.')
    }
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

migrateDisputes().catch(console.error)
