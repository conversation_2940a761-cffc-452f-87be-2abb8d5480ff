// Script to analyze the old dispute system and plan migration
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function analyzeOldDisputeSystem() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Analyzing Old Dispute System for Migration')
    console.log('=' .repeat(60))
    
    // 1. Get detailed info about existing disputes
    console.log('\n1. Existing verification disputes:')
    const disputes = await client.query(`
      SELECT 
        bv.id as verification_id,
        bv.company_benefit_id,
        bv.user_id,
        bv.status,
        bv.comment,
        bv.created_at,
        cb.is_verified as benefit_is_verified,
        cb.added_by,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.id as benefit_id,
        b.name as benefit_name,
        b.category as benefit_category,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      LEFT JOIN users u ON bv.user_id::uuid = u.id
      WHERE bv.status = 'disputed'
      ORDER BY bv.created_at DESC
    `)
    
    console.log(`   Found ${disputes.rows.length} disputed verifications:`)
    disputes.rows.forEach((dispute, index) => {
      console.log(`\n   Dispute ${index + 1}:`)
      console.log(`   - Benefit: ${dispute.benefit_name} (${dispute.benefit_category})`)
      console.log(`   - Company: ${dispute.company_name} (${dispute.company_domain})`)
      console.log(`   - User: ${dispute.user_first_name} ${dispute.user_last_name} (${dispute.user_email})`)
      console.log(`   - Comment: ${dispute.comment || 'No comment'}`)
      console.log(`   - Created: ${dispute.created_at}`)
      console.log(`   - Benefit verified: ${dispute.benefit_is_verified}`)
      console.log(`   - Added by: ${dispute.added_by || 'Unknown'}`)
    })
    
    // 2. Check how benefits are verified (user vs admin)
    console.log('\n\n2. Analyzing benefit verification methods:')
    const verificationMethods = await client.query(`
      SELECT 
        cb.is_verified,
        cb.added_by,
        COUNT(*) as count,
        CASE 
          WHEN cb.added_by IS NULL THEN 'Unknown'
          WHEN cb.added_by LIKE '%admin%' OR cb.added_by = 'system' THEN 'Admin/System'
          ELSE 'User'
        END as verification_type
      FROM company_benefits cb
      WHERE cb.is_verified = true
      GROUP BY cb.is_verified, cb.added_by, verification_type
      ORDER BY count DESC
    `)
    
    console.log('   Verification breakdown:')
    verificationMethods.rows.forEach(method => {
      console.log(`   - ${method.verification_type}: ${method.count} benefits (added_by: ${method.added_by || 'NULL'})`)
    })
    
    // 3. Check what functions/endpoints use the old dispute system
    console.log('\n\n3. Checking old dispute system usage:')
    
    // Check if there are any confirmed verifications that might be affected
    const confirmedVerifications = await client.query(`
      SELECT COUNT(*) as count FROM benefit_verifications WHERE status = 'confirmed'
    `)
    console.log(`   - Confirmed verifications: ${confirmedVerifications.rows[0].count}`)
    
    // Check total verifications
    const totalVerifications = await client.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM benefit_verifications 
      GROUP BY status
      ORDER BY count DESC
    `)
    console.log('   - Verification status breakdown:')
    totalVerifications.rows.forEach(status => {
      console.log(`     * ${status.status}: ${status.count}`)
    })
    
    // 4. Plan migration strategy
    console.log('\n\n4. Migration Strategy:')
    console.log('   For each disputed verification:')
    console.log('   a) If benefit is verified → Convert to removal dispute')
    console.log('   b) If benefit is not verified → Keep as verification dispute or remove')
    console.log('   c) Map user_id, company_benefit_id, and comment to new system')
    console.log('   d) Set status as "pending" for admin review')
    
    // 5. Check for admin-verified benefits
    console.log('\n\n5. Identifying admin-verified benefits:')
    const adminVerified = await client.query(`
      SELECT 
        cb.id,
        cb.added_by,
        b.name as benefit_name,
        c.name as company_name,
        cb.created_at
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE cb.is_verified = true 
      AND (cb.added_by IS NULL OR cb.added_by LIKE '%admin%' OR cb.added_by = 'system')
      ORDER BY cb.created_at DESC
      LIMIT 10
    `)
    
    console.log(`   Found ${adminVerified.rows.length} admin-verified benefits (showing first 10):`)
    adminVerified.rows.forEach(benefit => {
      console.log(`   - ${benefit.benefit_name} at ${benefit.company_name} (added_by: ${benefit.added_by || 'NULL'})`)
    })
    
    console.log('\n' + '=' .repeat(60))
    console.log('📋 Migration Plan Summary:')
    console.log(`   • Disputes to migrate: ${disputes.rows.length}`)
    console.log(`   • Admin-verified benefits to protect: ${adminVerified.rows.length}+`)
    console.log(`   • Total verifications in system: ${totalVerifications.rows.reduce((sum, row) => sum + parseInt(row.count), 0)}`)
    
  } catch (error) {
    console.error('❌ Analysis failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

analyzeOldDisputeSystem().catch(console.error)
