#!/usr/bin/env node

/**
 * Test script for dispute cancellation functionality
 * This script tests the complete dispute cancellation flow including:
 * - Creating a test dispute
 * - Cancelling the dispute
 * - Verifying authorization checks
 * - Testing edge cases
 */

const { Client } = require('pg')

async function main() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password'
  })

  try {
    await client.connect()
    console.log('🔗 Connected to database')

    // 1. Find a test user and benefit
    console.log('\n1. Finding test data...')
    
    const userResult = await client.query(`
      SELECT id, email, first_name, last_name 
      FROM users 
      WHERE email LIKE '%@adidas.de' 
      LIMIT 1
    `)
    
    if (userResult.rows.length === 0) {
      console.log('❌ No test user found. Creating one...')
      
      // Create a test user
      const newUserResult = await client.query(`
        INSERT INTO users (email, first_name, last_name, role)
        VALUES ('<EMAIL>', 'Test', 'Cancellation', 'user')
        RETURNING *
      `)
      
      console.log(`✅ Created test user: ${newUserResult.rows[0].email}`)
    }
    
    const user = userResult.rows[0] || (await client.query(`
      SELECT id, email, first_name, last_name 
      FROM users 
      WHERE email = '<EMAIL>'
    `)).rows[0]
    
    console.log(`   User: ${user.email} (ID: ${user.id})`)

    // Find a verified benefit at Adidas
    const benefitResult = await client.query(`
      SELECT 
        cb.id as company_benefit_id,
        b.name as benefit_name,
        c.name as company_name,
        c.domain as company_domain
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE c.domain = 'adidas.de' 
        AND cb.is_verified = true
      LIMIT 1
    `)
    
    if (benefitResult.rows.length === 0) {
      console.log('❌ No verified benefits found for Adidas')
      return
    }
    
    const benefit = benefitResult.rows[0]
    console.log(`   Benefit: ${benefit.benefit_name} at ${benefit.company_name}`)

    // 2. Create a test session for the user
    console.log('\n2. Creating test session...')
    
    const sessionResult = await client.query(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES ($1, $2, NOW() + INTERVAL '1 hour')
      RETURNING session_token
    `, [user.id, `test_session_${Date.now()}`])
    
    const sessionToken = sessionResult.rows[0].session_token
    console.log(`   Session token: ${sessionToken}`)

    // 3. Submit a test dispute
    console.log('\n3. Submitting test dispute...')
    
    const fetch = (await import('node-fetch')).default
    
    const submitResponse = await fetch('http://localhost:3001/api/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${sessionToken}`
      },
      body: JSON.stringify({
        companyBenefitId: benefit.company_benefit_id,
        reason: 'Test dispute for cancellation functionality testing'
      })
    })
    
    console.log(`   Submit response status: ${submitResponse.status}`)
    
    if (!submitResponse.ok) {
      const error = await submitResponse.json()
      console.log(`❌ Failed to submit dispute: ${error.error}`)
      return
    }
    
    const submitData = await submitResponse.json()
    const disputeId = submitData.dispute.id
    console.log(`   ✅ Dispute created with ID: ${disputeId}`)

    // 4. Test cancellation
    console.log('\n4. Testing dispute cancellation...')

    const cancelResponse = await fetch('http://localhost:3001/api/benefit-removal-disputes/cancel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${sessionToken}`
      },
      body: JSON.stringify({
        disputeId: disputeId
      })
    })
    
    console.log(`   Cancel response status: ${cancelResponse.status}`)
    
    if (cancelResponse.ok) {
      const cancelData = await cancelResponse.json()
      console.log(`   ✅ ${cancelData.message}`)
      
      // Verify the dispute status was updated
      const verifyResult = await client.query(`
        SELECT status, updated_at 
        FROM benefit_removal_disputes 
        WHERE id = $1
      `, [disputeId])
      
      if (verifyResult.rows.length > 0) {
        const dispute = verifyResult.rows[0]
        console.log(`   ✅ Dispute status: ${dispute.status}`)
        console.log(`   ✅ Updated at: ${dispute.updated_at}`)
      }
    } else {
      const error = await cancelResponse.json()
      console.log(`   ❌ Failed to cancel dispute: ${error.error}`)
    }

    // 5. Test authorization - try to cancel with different user
    console.log('\n5. Testing authorization (should fail)...')
    
    // Create another user
    const otherUserResult = await client.query(`
      INSERT INTO users (email, first_name, last_name, role)
      VALUES ('<EMAIL>', 'Other', 'User', 'user')
      ON CONFLICT (email) DO UPDATE SET email = EXCLUDED.email
      RETURNING *
    `)
    
    const otherUser = otherUserResult.rows[0]
    
    // Create session for other user
    const otherSessionResult = await client.query(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES ($1, $2, NOW() + INTERVAL '1 hour')
      RETURNING session_token
    `, [otherUser.id, `other_session_${Date.now()}`])
    
    const otherSessionToken = otherSessionResult.rows[0].session_token
    
    // Submit another dispute to test with
    const submitResponse2 = await fetch('http://localhost:3001/api/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${otherSessionToken}`
      },
      body: JSON.stringify({
        companyBenefitId: benefit.company_benefit_id,
        reason: 'Another test dispute for authorization testing'
      })
    })
    
    if (submitResponse2.ok) {
      const submitData2 = await submitResponse2.json()
      const disputeId2 = submitData2.dispute.id
      
      // Try to cancel with original user (should fail)
      const unauthorizedCancelResponse = await fetch('http://localhost:3001/api/benefit-removal-disputes/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session_token=${sessionToken}`
        },
        body: JSON.stringify({
          disputeId: disputeId2
        })
      })
      
      if (unauthorizedCancelResponse.status === 403) {
        console.log(`   ✅ Authorization check working - got 403 as expected`)
      } else {
        console.log(`   ❌ Authorization check failed - got ${unauthorizedCancelResponse.status}`)
      }
      
      // Clean up the second dispute
      await client.query('DELETE FROM benefit_removal_disputes WHERE id = $1', [disputeId2])
    }

    // 6. Test UI data after cancellation
    console.log('\n6. Testing UI data after cancellation...')

    const uiDataResponse = await fetch(`http://localhost:3001/api/benefit-removal-disputes/${benefit.company_benefit_id}`, {
      headers: {
        'Cookie': `session_token=${sessionToken}`
      }
    })

    if (uiDataResponse.ok) {
      const uiData = await uiDataResponse.json()
      console.log(`   ✅ UI data retrieved successfully`)
      console.log(`   User dispute status: ${uiData.userDispute ? uiData.userDispute.status : 'none'}`)
      console.log(`   Can dispute: ${uiData.canDispute}`)
      console.log(`   Stats - Pending: ${uiData.stats.pending}, Approved: ${uiData.stats.approved}, Rejected: ${uiData.stats.rejected}`)

      // Verify that cancelled dispute doesn't affect UI logic
      if (uiData.userDispute && uiData.userDispute.status === 'cancelled') {
        console.log(`   ✅ Cancelled dispute is returned in data (for reference)`)
      }

      if (uiData.canDispute) {
        console.log(`   ✅ User can submit new dispute after cancellation`)
      } else {
        console.log(`   ❌ User should be able to submit new dispute after cancellation`)
      }
    } else {
      console.log(`   ❌ Failed to get UI data`)
    }

    // 7. Check activity logging
    console.log('\n7. Checking activity logging...')
    
    const activityResult = await client.query(`
      SELECT 
        event_type,
        event_description,
        user_email,
        benefit_name,
        company_name,
        created_at,
        metadata
      FROM activity_log
      WHERE event_type = 'benefit_removal_dispute_cancelled'
        AND user_id = $1
      ORDER BY created_at DESC
      LIMIT 1
    `, [user.id])
    
    if (activityResult.rows.length > 0) {
      const log = activityResult.rows[0]
      console.log(`   ✅ Activity logged successfully:`)
      console.log(`      Event: ${log.event_type}`)
      console.log(`      Description: ${log.event_description}`)
      console.log(`      User: ${log.user_email}`)
      console.log(`      Benefit: ${log.benefit_name} at ${log.company_name}`)
      console.log(`      Metadata: ${JSON.stringify(log.metadata)}`)
    } else {
      console.log(`   ❌ No activity log found for cancellation`)
    }

    // 8. Clean up
    console.log('\n8. Cleaning up test data...')
    
    // Delete test disputes
    await client.query('DELETE FROM benefit_removal_disputes WHERE user_id = $1', [user.id])
    
    // Delete test sessions
    await client.query('DELETE FROM user_sessions WHERE user_id IN ($1, $2)', [user.id, otherUser.id])
    
    // Delete test users
    await client.query('DELETE FROM users WHERE id IN ($1, $2)', [user.id, otherUser.id])
    
    console.log('   ✅ Test data cleaned up')
    
    console.log('\n🎉 Dispute cancellation test completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await client.end()
  }
}

if (require.main === module) {
  main()
}
