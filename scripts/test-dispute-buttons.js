// Test script to check if dispute buttons should be showing
const BASE_URL = 'http://localhost:3002'

async function testDisputeButtons() {
  console.log('🧪 Testing Dispute Button Visibility')
  console.log('=' .repeat(50))
  
  try {
    // Test 1: Get companies to find one with verified benefits
    console.log('\n1. Finding companies with verified benefits...')
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=10`)
    
    if (!companiesResponse.ok) {
      console.log('❌ Failed to fetch companies')
      return
    }
    
    const companiesData = await companiesResponse.json()
    console.log(`   Found ${companiesData.companies?.length || 0} companies`)
    
    if (!companiesData.companies || companiesData.companies.length === 0) {
      console.log('❌ No companies found')
      return
    }
    
    // Test 2: Check benefits for each company
    for (const company of companiesData.companies.slice(0, 3)) {
      console.log(`\n2. Checking benefits for ${company.name}...`)
      
      const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${company.id}/benefits`)
      
      if (!benefitsResponse.ok) {
        console.log(`   ❌ Failed to fetch benefits for ${company.name}`)
        continue
      }
      
      const benefits = await benefitsResponse.json()
      console.log(`   Found ${benefits.length} benefits`)
      
      const verifiedBenefits = benefits.filter(b => b.is_verified)
      const adminVerifiedBenefits = benefits.filter(b => b.is_admin_verified)
      
      console.log(`   - Verified benefits: ${verifiedBenefits.length}`)
      console.log(`   - Admin-verified benefits: ${adminVerifiedBenefits.length}`)
      
      if (verifiedBenefits.length > 0) {
        const testBenefit = verifiedBenefits[0]
        console.log(`   - Testing benefit: ${testBenefit.name}`)
        console.log(`   - Is verified: ${testBenefit.is_verified}`)
        console.log(`   - Is admin verified: ${testBenefit.is_admin_verified}`)
        
        // Test 3: Check dispute status for this benefit
        console.log(`\n3. Testing dispute status for ${testBenefit.name}...`)
        const disputeResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${testBenefit.id}`)
        
        if (disputeResponse.ok) {
          const disputeData = await disputeResponse.json()
          console.log(`   ✅ Dispute API accessible`)
          console.log(`   - Can dispute: ${disputeData.canDispute}`)
          console.log(`   - User dispute: ${disputeData.userDispute ? 'Yes' : 'No'}`)
          console.log(`   - Stats: ${JSON.stringify(disputeData.stats)}`)
          
          if (!disputeData.canDispute) {
            console.log(`   ⚠️  Cannot dispute - this might be why buttons are missing`)
            console.log(`   - Benefit verified: ${disputeData.benefit?.is_verified}`)
            console.log(`   - Company: ${disputeData.benefit?.company_name}`)
          }
        } else if (disputeResponse.status === 401) {
          console.log(`   ⚠️  Authentication required - user not signed in`)
          console.log(`   - This explains why dispute buttons are missing`)
        } else {
          console.log(`   ❌ Dispute API error: ${disputeResponse.status}`)
        }
        
        break // Only test first company with verified benefits
      }
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log('📋 Summary:')
    console.log('   If "Cannot dispute" or "Authentication required":')
    console.log('   → This explains why dispute buttons are missing')
    console.log('   → Users need to sign in with company email to see buttons')
    console.log('   → Buttons should appear for verified benefits when authenticated')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testDisputeButtons()
