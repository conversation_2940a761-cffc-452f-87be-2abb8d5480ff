// Test admin dispute approval functionality
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function testAdminDisputeApproval() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Testing Admin Dispute Approval')
    console.log('=' .repeat(40))
    
    // Get an admin session token
    const adminSession = await client.query(`
      SELECT us.session_token, u.email, u.role
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE u.role = 'admin' 
      AND us.expires_at > NOW()
      ORDER BY us.expires_at DESC
      LIMIT 1
    `)
    
    if (adminSession.rows.length === 0) {
      console.log('❌ No active admin sessions found')
      console.log('   Please sign <NAME_EMAIL> first')
      return
    }
    
    const sessionToken = adminSession.rows[0].session_token
    const adminEmail = adminSession.rows[0].email
    
    console.log(`Found admin session for: ${adminEmail}`)
    
    // Get a pending dispute to test with
    const pendingDispute = await client.query(`
      SELECT 
        brd.id,
        brd.reason,
        brd.status,
        b.name as benefit_name,
        c.name as company_name
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE brd.status = 'pending'
      ORDER BY brd.created_at DESC
      LIMIT 1
    `)
    
    if (pendingDispute.rows.length === 0) {
      console.log('❌ No pending disputes found to test with')
      return
    }
    
    const dispute = pendingDispute.rows[0]
    console.log(`\nTesting with dispute: ${dispute.benefit_name} at ${dispute.company_name}`)
    console.log(`Dispute ID: ${dispute.id}`)
    console.log(`Reason: ${dispute.reason}`)
    console.log(`Current status: ${dispute.status}`)
    
    // Test approving the dispute
    console.log('\nTesting dispute approval...')
    
    const fetch = (await import('node-fetch')).default
    
    const response = await fetch('http://localhost:3002/api/admin/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${sessionToken}`
      },
      body: JSON.stringify({
        disputeId: dispute.id,
        action: 'approve',
        adminComment: 'Test approval - verifying the fix works'
      })
    })
    
    console.log(`Response status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Dispute approval successful!')
      console.log(`Message: ${data.message}`)
      
      // Check if the dispute was actually updated
      const updatedDispute = await client.query(`
        SELECT 
          status, 
          admin_user_id, 
          admin_comment,
          updated_at
        FROM benefit_removal_disputes 
        WHERE id = $1
      `, [dispute.id])
      
      if (updatedDispute.rows.length > 0) {
        const updated = updatedDispute.rows[0]
        console.log('\n📋 Updated dispute details:')
        console.log(`  Status: ${updated.status}`)
        console.log(`  Admin User ID: ${updated.admin_user_id}`)
        console.log(`  Admin Comment: ${updated.admin_comment}`)
        console.log(`  Updated At: ${updated.updated_at}`)
        
        if (updated.status === 'approved') {
          console.log('✅ Dispute status correctly updated to approved')
        } else {
          console.log('❌ Dispute status was not updated correctly')
        }
      }
      
      // Revert the dispute back to pending for future tests
      console.log('\n🔄 Reverting dispute back to pending for future tests...')
      await client.query(`
        UPDATE benefit_removal_disputes
        SET status = 'pending', admin_user_id = NULL, admin_comment = NULL
        WHERE id = $1
      `, [dispute.id])
      console.log('Dispute reverted to pending status')
      
    } else {
      const errorText = await response.text()
      console.log('❌ Dispute approval failed')
      console.log(`Error: ${errorText}`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

testAdminDisputeApproval().catch(console.error)
