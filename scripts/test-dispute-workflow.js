// Test script for benefit removal dispute workflow
// This script tests the complete workflow through the API endpoints

const BASE_URL = 'http://localhost:3002'

async function testDisputeWorkflow() {
  console.log('🧪 Testing Benefit Removal Dispute Workflow')
  console.log('=' .repeat(50))
  
  try {
    // Test 1: Check if the API endpoints are accessible
    console.log('\n1. Testing API endpoint accessibility...')
    
    // Test the dispute status endpoint (should work without auth for basic info)
    const testBenefitId = 'test-benefit-id'
    const statusResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${testBenefitId}`)
    
    if (statusResponse.status === 401) {
      console.log('✅ API endpoints are accessible (auth required as expected)')
    } else if (statusResponse.status === 404) {
      console.log('✅ API endpoints are accessible (benefit not found as expected)')
    } else {
      console.log(`⚠️  Unexpected status: ${statusResponse.status}`)
    }
    
    // Test 2: Check admin endpoints
    console.log('\n2. Testing admin endpoint accessibility...')
    const adminResponse = await fetch(`${BASE_URL}/api/admin/benefit-removal-disputes`)
    
    if (adminResponse.status === 401 || adminResponse.status === 403) {
      console.log('✅ Admin endpoints are protected (auth required as expected)')
    } else {
      console.log(`⚠️  Unexpected admin status: ${adminResponse.status}`)
    }
    
    // Test 3: Check database schema (through a simple API call)
    console.log('\n3. Testing database schema...')
    
    // Try to access a company endpoint to see if the database is working
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=1`)
    
    if (companiesResponse.ok) {
      console.log('✅ Database connection is working')
      
      // Check if we can access the companies data
      const companiesData = await companiesResponse.json()
      console.log(`   Found ${companiesData.companies?.length || 0} companies`)
      
      if (companiesData.companies && companiesData.companies.length > 0) {
        const testCompany = companiesData.companies[0]
        console.log(`   Test company: ${testCompany.name}`)
        
        // Test 4: Check company benefits endpoint
        console.log('\n4. Testing company benefits endpoint...')
        const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${testCompany.id}/benefits`)
        
        if (benefitsResponse.ok) {
          const benefitsData = await benefitsResponse.json()
          console.log(`✅ Company benefits endpoint working (${benefitsData.length || 0} benefits)`)
          
          if (benefitsData.length > 0) {
            const testBenefit = benefitsData.find(b => b.is_verified) || benefitsData[0]
            console.log(`   Test benefit: ${testBenefit.name} (verified: ${testBenefit.is_verified})`)
            
            // Test 5: Check dispute status for a real benefit
            console.log('\n5. Testing dispute status for real benefit...')
            const realDisputeResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${testBenefit.id}`)
            
            if (realDisputeResponse.status === 401) {
              console.log('✅ Dispute status endpoint requires authentication')
            } else if (realDisputeResponse.ok) {
              const disputeData = await realDisputeResponse.json()
              console.log('✅ Dispute status endpoint accessible')
              console.log(`   Can dispute: ${disputeData.canDispute}`)
              console.log(`   Stats: ${JSON.stringify(disputeData.stats)}`)
            } else {
              console.log(`⚠️  Dispute status error: ${realDisputeResponse.status}`)
            }
          }
        } else {
          console.log(`❌ Company benefits endpoint error: ${benefitsResponse.status}`)
        }
      }
    } else {
      console.log(`❌ Database connection issue: ${companiesResponse.status}`)
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 Dispute workflow test completed!')
    console.log('\nNext steps for manual testing:')
    console.log('1. Sign in to the application with a company email')
    console.log('2. Navigate to a company page with verified benefits')
    console.log('3. Look for "Request Removal" buttons on verified benefits')
    console.log('4. Submit a dispute and check the admin panel')
    console.log('5. Test the admin approval workflow')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testDisputeWorkflow()
