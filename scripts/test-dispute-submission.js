// Test dispute submission and check if activity logging works
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function testDisputeSubmission() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Testing Dispute Submission and Activity Logging')
    console.log('=' .repeat(50))
    
    // Get an active user session and a verified benefit to dispute
    const userSession = await client.query(`
      SELECT 
        us.session_token,
        us.user_id,
        u.email,
        u.first_name,
        u.last_name
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE us.expires_at > NOW()
      AND u.role != 'admin'
      ORDER BY us.expires_at DESC
      LIMIT 1
    `)
    
    if (userSession.rows.length === 0) {
      console.log('❌ No active user sessions found')
      console.log('   Please sign in as a regular user first')
      return
    }
    
    const session = userSession.rows[0]
    console.log(`Found user session: ${session.email}`)
    
    // Get a verified benefit from the user's company domain
    const userDomain = session.email.split('@')[1]
    
    const verifiedBenefit = await client.query(`
      SELECT 
        cb.id,
        b.name as benefit_name,
        c.name as company_name,
        c.domain
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE cb.is_verified = true
      AND c.domain = $1
      AND cb.id NOT IN (
        SELECT company_benefit_id 
        FROM benefit_removal_disputes 
        WHERE user_id = $2
      )
      LIMIT 1
    `, [userDomain, session.user_id])
    
    if (verifiedBenefit.rows.length === 0) {
      console.log(`❌ No verified benefits found for domain ${userDomain} that user hasn't already disputed`)
      return
    }
    
    const benefit = verifiedBenefit.rows[0]
    console.log(`Found benefit to dispute: ${benefit.benefit_name} at ${benefit.company_name}`)
    
    // Count activity logs before submission
    const logsBefore = await client.query(`
      SELECT COUNT(*) as count FROM activity_log
      WHERE event_type = 'benefit_removal_dispute_submitted'
    `)
    
    console.log(`Activity logs before: ${logsBefore.rows[0].count}`)
    
    // Submit a dispute via API
    console.log('\nSubmitting dispute via API...')
    
    const fetch = (await import('node-fetch')).default
    
    const response = await fetch('http://localhost:3002/api/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${session.session_token}`
      },
      body: JSON.stringify({
        companyBenefitId: benefit.id,
        reason: 'Test dispute for activity logging verification'
      })
    })
    
    console.log(`Response status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Dispute submitted successfully')
      console.log(`Dispute ID: ${data.dispute.id}`)
      
      // Wait a moment for logging to complete
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Count activity logs after submission
      const logsAfter = await client.query(`
        SELECT COUNT(*) as count FROM activity_log
        WHERE event_type = 'benefit_removal_dispute_submitted'
      `)
      
      console.log(`Activity logs after: ${logsAfter.rows[0].count}`)
      
      if (parseInt(logsAfter.rows[0].count) > parseInt(logsBefore.rows[0].count)) {
        console.log('✅ Activity logging is working!')
        
        // Show the new log entry
        const newLog = await client.query(`
          SELECT 
            event_type,
            event_description,
            user_email,
            benefit_name,
            company_name,
            created_at,
            metadata
          FROM activity_log
          WHERE event_type = 'benefit_removal_dispute_submitted'
          ORDER BY created_at DESC
          LIMIT 1
        `)
        
        if (newLog.rows.length > 0) {
          const log = newLog.rows[0]
          console.log('\nNew activity log entry:')
          console.log(`  Event: ${log.event_type}`)
          console.log(`  Description: ${log.event_description}`)
          console.log(`  User: ${log.user_email}`)
          console.log(`  Benefit: ${log.benefit_name} at ${log.company_name}`)
          console.log(`  Time: ${log.created_at}`)
          console.log(`  Metadata: ${JSON.stringify(log.metadata)}`)
        }
      } else {
        console.log('❌ Activity logging is NOT working!')
        console.log('   The dispute was submitted but no activity log was created.')
      }
      
      // Clean up - remove the test dispute
      await client.query('DELETE FROM benefit_removal_disputes WHERE id = $1', [data.dispute.id])
      console.log('\n🧹 Test dispute cleaned up')
      
    } else {
      const errorText = await response.text()
      console.log('❌ Dispute submission failed')
      console.log(`Error: ${errorText}`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

testDisputeSubmission().catch(console.error)
