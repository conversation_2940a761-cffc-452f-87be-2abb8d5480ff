// Fix the activity_log event_type constraint to include new event types
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function fixActivityLogConstraint() {
  const client = await pool.connect()
  
  try {
    console.log('🔧 Fixing Activity Log Event Type Constraint')
    console.log('=' .repeat(50))
    
    await client.query('BEGIN')
    
    // 1. Check current constraint
    console.log('\n1. Checking current constraint...')
    const currentConstraint = await client.query(`
      SELECT pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint
      WHERE conname = 'activity_log_event_type_check'
      AND conrelid = 'activity_log'::regclass
    `)
    
    if (currentConstraint.rows.length > 0) {
      console.log('   Current constraint:')
      console.log(`   ${currentConstraint.rows[0].constraint_definition}`)
    } else {
      console.log('   ❌ Constraint not found')
      return
    }
    
    // 2. Drop the old constraint
    console.log('\n2. Dropping old constraint...')
    await client.query(`
      ALTER TABLE activity_log 
      DROP CONSTRAINT activity_log_event_type_check
    `)
    console.log('   ✅ Old constraint dropped')
    
    // 3. Create new constraint with all event types
    console.log('\n3. Creating new constraint with all event types...')
    
    const newConstraint = `
      ALTER TABLE activity_log 
      ADD CONSTRAINT activity_log_event_type_check 
      CHECK (event_type IN (
        'company_added',
        'user_registered', 
        'benefit_verified',
        'benefit_disputed',
        'benefit_removal_dispute_submitted',
        'benefit_removal_dispute_approved',
        'benefit_removal_dispute_rejected',
        'benefit_automatically_removed'
      ))
    `
    
    await client.query(newConstraint)
    console.log('   ✅ New constraint created')
    
    // 4. Verify the new constraint
    console.log('\n4. Verifying new constraint...')
    const newConstraintDef = await client.query(`
      SELECT pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint
      WHERE conname = 'activity_log_event_type_check'
      AND conrelid = 'activity_log'::regclass
    `)
    
    if (newConstraintDef.rows.length > 0) {
      console.log('   New constraint:')
      console.log(`   ${newConstraintDef.rows[0].constraint_definition}`)
    }
    
    // 5. Test the new constraint by trying to insert a test record
    console.log('\n5. Testing new constraint...')
    
    try {
      await client.query(`
        INSERT INTO activity_log (
          event_type, 
          event_description, 
          user_email
        ) VALUES (
          'benefit_removal_dispute_submitted',
          'Test entry to verify constraint',
          '<EMAIL>'
        )
      `)
      
      console.log('   ✅ Test insert successful - new event types are allowed')
      
      // Clean up test record
      await client.query(`
        DELETE FROM activity_log 
        WHERE event_description = 'Test entry to verify constraint'
      `)
      
    } catch (testError) {
      console.log('   ❌ Test insert failed:', testError.message)
      throw testError
    }
    
    await client.query('COMMIT')
    
    console.log('\n' + '=' .repeat(50))
    console.log('✅ Activity log constraint fixed successfully!')
    console.log('   • Old constraint removed')
    console.log('   • New constraint includes all event types')
    console.log('   • benefit_removal_dispute_submitted is now allowed')
    console.log('   • Activity logging should now work for disputes')
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Fix failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

fixActivityLogConstraint().catch(console.error)
