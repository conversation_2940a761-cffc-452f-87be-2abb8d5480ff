// Check if dispute submissions are being logged correctly
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkDisputeLogging() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Dispute Logging')
    console.log('=' .repeat(40))
    
    // Get all disputes ordered by creation time
    const disputes = await client.query(`
      SELECT 
        brd.id,
        brd.reason,
        brd.created_at,
        b.name as benefit_name,
        c.name as company_name,
        u.email as user_email,
        u.first_name,
        u.last_name
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      LEFT JOIN users u ON brd.user_id = u.id
      ORDER BY brd.created_at DESC
    `)
    
    console.log(`Found ${disputes.rows.length} disputes:`)
    
    for (const dispute of disputes.rows) {
      console.log(`\n📋 Dispute: ${dispute.benefit_name} at ${dispute.company_name}`)
      console.log(`   User: ${dispute.user_email} (${dispute.first_name} ${dispute.last_name})`)
      console.log(`   Reason: ${dispute.reason}`)
      console.log(`   Created: ${dispute.created_at}`)
      
      // Check if this dispute has a corresponding activity log
      const activityLogs = await client.query(`
        SELECT 
          event_type,
          event_description,
          created_at,
          metadata
        FROM activity_log
        WHERE user_email = $1 
        AND benefit_name = $2
        AND company_name = $3
        AND event_type LIKE '%dispute%'
        ORDER BY created_at DESC
      `, [dispute.user_email, dispute.benefit_name, dispute.company_name])
      
      if (activityLogs.rows.length > 0) {
        console.log(`   ✅ Found ${activityLogs.rows.length} activity log(s):`)
        activityLogs.rows.forEach((log, index) => {
          console.log(`      ${index + 1}. ${log.event_type}: ${log.event_description}`)
          console.log(`         Logged at: ${log.created_at}`)
          if (log.metadata) {
            console.log(`         Metadata: ${JSON.stringify(log.metadata)}`)
          }
        })
      } else {
        console.log(`   ❌ No activity log found for this dispute`)
      }
    }
    
    // Check all dispute-related activity logs
    console.log('\n' + '=' .repeat(40))
    console.log('All dispute-related activity logs:')
    
    const allDisputeLogs = await client.query(`
      SELECT 
        event_type,
        event_description,
        user_email,
        benefit_name,
        company_name,
        created_at,
        metadata
      FROM activity_log
      WHERE event_type LIKE '%dispute%'
      ORDER BY created_at DESC
    `)
    
    console.log(`\nFound ${allDisputeLogs.rows.length} dispute-related logs:`)
    allDisputeLogs.rows.forEach((log, index) => {
      console.log(`\n${index + 1}. ${log.event_type}`)
      console.log(`   Description: ${log.event_description}`)
      console.log(`   User: ${log.user_email}`)
      console.log(`   Benefit: ${log.benefit_name} at ${log.company_name}`)
      console.log(`   Time: ${log.created_at}`)
      if (log.metadata) {
        console.log(`   Metadata: ${JSON.stringify(log.metadata)}`)
      }
    })
    
    // Summary
    console.log('\n' + '=' .repeat(40))
    console.log('📊 Summary:')
    console.log(`   Total disputes: ${disputes.rows.length}`)
    console.log(`   Total dispute logs: ${allDisputeLogs.rows.length}`)
    
    if (disputes.rows.length > allDisputeLogs.rows.length) {
      console.log(`   ❌ Missing ${disputes.rows.length - allDisputeLogs.rows.length} dispute log(s)`)
      console.log('   Some dispute submissions are not being logged!')
    } else if (disputes.rows.length === allDisputeLogs.rows.length) {
      console.log('   ✅ All disputes appear to be logged')
    } else {
      console.log('   ⚠️  More logs than disputes (possibly includes old system logs)')
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkDisputeLogging().catch(console.error)
