// Test the complete workflow after fixes
const BASE_URL = 'http://localhost:3002'

async function testCompleteWorkflow() {
  console.log('🧪 Testing Complete Workflow After Fixes')
  console.log('=' .repeat(50))
  
  try {
    // Test 1: Find companies with verified benefits
    console.log('\n1. Finding companies with verified benefits...')
    const companiesResponse = await fetch(`${BASE_URL}/api/companies`)
    const companies = await companiesResponse.json()
    
    console.log(`   Found ${companies.length} companies`)
    
    let testedBenefits = 0
    let workingDisputes = 0
    
    // Test first few companies
    for (let i = 0; i < Math.min(companies.length, 5); i++) {
      const company = companies[i]
      console.log(`\n2. Testing ${company.name}...`)
      
      const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${company.id}/benefits`)
      if (!benefitsResponse.ok) continue
      
      const benefits = await benefitsResponse.json()
      const verifiedBenefits = benefits.filter(b => b.is_verified)
      const adminVerifiedBenefits = benefits.filter(b => b.is_admin_verified)
      
      console.log(`   - Total benefits: ${benefits.length}`)
      console.log(`   - Verified benefits: ${verifiedBenefits.length}`)
      console.log(`   - Admin-verified benefits: ${adminVerifiedBenefits.length}`)
      
      if (verifiedBenefits.length > 0) {
        // Test dispute API for verified benefits
        for (let j = 0; j < Math.min(verifiedBenefits.length, 3); j++) {
          const benefit = verifiedBenefits[j]
          testedBenefits++
          
          console.log(`\n   Testing dispute API for: ${benefit.name}`)
          const disputeResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${benefit.id}`)
          
          if (disputeResponse.ok) {
            const disputeData = await disputeResponse.json()
            console.log(`   ✅ API working - canDispute: ${disputeData.canDispute}`)
            console.log(`   - Benefit verified: ${disputeData.benefit.is_verified}`)
            console.log(`   - Requires approvals: ${disputeData.requiresApprovals}`)
            console.log(`   - Current disputes: ${disputeData.stats.total}`)
            workingDisputes++
            
            if (disputeData.canDispute === false) {
              console.log(`   💡 canDispute=false is correct (user not authenticated)`)
            }
          } else {
            console.log(`   ❌ API error: ${disputeResponse.status}`)
          }
        }
        
        if (testedBenefits >= 5) break // Test enough benefits
      }
    }
    
    // Test 3: Check migration results
    console.log('\n3. Checking migration results...')
    
    const { Pool } = require('pg')
    const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'
    
    const pool = new Pool({ connectionString: DATABASE_URL })
    const client = await pool.connect()
    
    try {
      // Check old disputes
      const oldDisputes = await client.query(`
        SELECT COUNT(*) as count FROM benefit_verifications WHERE status = 'disputed'
      `)
      
      // Check new disputes
      const newDisputes = await client.query(`
        SELECT COUNT(*) as count FROM benefit_removal_disputes
      `)
      
      // Check user_id type
      const userIdType = await client.query(`
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'benefit_removal_disputes' 
        AND column_name = 'user_id'
      `)
      
      console.log(`   Old disputes remaining: ${oldDisputes.rows[0].count}`)
      console.log(`   New disputes created: ${newDisputes.rows[0].count}`)
      console.log(`   user_id column type: ${userIdType.rows[0].data_type}`)
      
      if (oldDisputes.rows[0].count === '0' && newDisputes.rows[0].count >= '2') {
        console.log('   ✅ Migration completed successfully')
      } else {
        console.log('   ⚠️  Migration may be incomplete')
      }
      
      if (userIdType.rows[0].data_type === 'uuid') {
        console.log('   ✅ Database schema fixed')
      } else {
        console.log('   ❌ Database schema still has issues')
      }
      
    } finally {
      client.release()
      await pool.end()
    }
    
    // Test 4: Summary
    console.log('\n' + '=' .repeat(50))
    console.log('📋 Test Results Summary:')
    console.log(`   • Companies tested: ${Math.min(companies.length, 5)}`)
    console.log(`   • Benefits tested: ${testedBenefits}`)
    console.log(`   • Working dispute APIs: ${workingDisputes}`)
    console.log(`   • API success rate: ${testedBenefits > 0 ? Math.round((workingDisputes / testedBenefits) * 100) : 0}%`)
    
    if (workingDisputes === testedBenefits && testedBenefits > 0) {
      console.log('\n🎉 All tests passed!')
      console.log('✅ Dispute buttons should now appear for verified benefits')
      console.log('✅ Migration completed successfully')
      console.log('✅ Database schema fixed')
      console.log('\n📝 Next steps:')
      console.log('   1. Sign in with a company email (e.g., @adidas.de)')
      console.log('   2. Navigate to a company page with verified benefits')
      console.log('   3. Look for "Request Removal" buttons on verified benefits')
      console.log('   4. Test the dispute submission workflow')
    } else {
      console.log('\n⚠️  Some issues remain:')
      if (testedBenefits === 0) {
        console.log('   - No verified benefits found to test')
      } else if (workingDisputes < testedBenefits) {
        console.log('   - Some dispute APIs still failing')
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testCompleteWorkflow()
