// Test API endpoints directly
const BASE_URL = 'http://localhost:3002'

async function testAPI() {
  console.log('🧪 Testing API Endpoints Directly')
  console.log('=' .repeat(40))
  
  try {
    // Test companies API
    console.log('\n1. Testing companies API...')
    const companiesResponse = await fetch(`${BASE_URL}/api/companies`)
    console.log(`   Status: ${companiesResponse.status}`)
    
    if (companiesResponse.ok) {
      const companiesData = await companiesResponse.json()
      console.log(`   Response type: ${typeof companiesData}`)
      console.log(`   Response keys: ${Object.keys(companiesData)}`)
      
      if (Array.isArray(companiesData)) {
        console.log(`   ✅ Direct array with ${companiesData.length} companies`)
        if (companiesData.length > 0) {
          console.log(`   Sample: ${companiesData[0].name}`)
        }
      } else if (companiesData.companies) {
        console.log(`   ✅ Object with companies array: ${companiesData.companies.length} companies`)
        if (companiesData.companies.length > 0) {
          console.log(`   Sample: ${companiesData.companies[0].name}`)
        }
      } else {
        console.log(`   ⚠️  Unexpected format: ${JSON.stringify(companiesData).substring(0, 200)}...`)
      }
    } else {
      const errorText = await companiesResponse.text()
      console.log(`   ❌ Error: ${errorText}`)
    }
    
    // Test a specific company's benefits
    console.log('\n2. Testing company benefits API...')
    
    // First get a company ID
    const companiesForBenefits = await fetch(`${BASE_URL}/api/companies`)
    if (companiesForBenefits.ok) {
      const companiesData = await companiesForBenefits.json()
      const companies = Array.isArray(companiesData) ? companiesData : companiesData.companies || []
      
      if (companies.length > 0) {
        const testCompany = companies[0]
        console.log(`   Testing benefits for: ${testCompany.name}`)
        
        const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${testCompany.id}/benefits`)
        console.log(`   Benefits API status: ${benefitsResponse.status}`)
        
        if (benefitsResponse.ok) {
          const benefits = await benefitsResponse.json()
          console.log(`   Found ${benefits.length} benefits`)
          
          const verifiedBenefits = benefits.filter(b => b.is_verified)
          console.log(`   Verified benefits: ${verifiedBenefits.length}`)
          
          if (verifiedBenefits.length > 0) {
            const testBenefit = verifiedBenefits[0]
            console.log(`   Testing dispute API for: ${testBenefit.name}`)
            
            // Test dispute API
            const disputeResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${testBenefit.id}`)
            console.log(`   Dispute API status: ${disputeResponse.status}`)
            
            if (disputeResponse.status === 401) {
              console.log(`   ⚠️  Authentication required (expected for unauthenticated request)`)
            } else if (disputeResponse.ok) {
              const disputeData = await disputeResponse.json()
              console.log(`   Dispute data: ${JSON.stringify(disputeData, null, 2)}`)
            } else {
              const errorText = await disputeResponse.text()
              console.log(`   ❌ Dispute API error: ${errorText}`)
            }
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message)
  }
}

testAPI()
