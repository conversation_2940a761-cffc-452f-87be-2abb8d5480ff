// Test the SQL query that's failing in the API
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function testSQLQuery() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Testing SQL Query from API')
    console.log('=' .repeat(40))
    
    const companyBenefitId = 'c59dd830-7cd4-41d0-b38a-14046f1d65d8'
    
    // Test the benefit query from the API
    console.log('\n1. Testing benefit query...')
    const benefitResult = await client.query(`
      SELECT 
        cb.id,
        cb.is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.name as benefit_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.id = $1
    `, [companyBenefitId])
    
    if (benefitResult.rows.length === 0) {
      console.log('   ❌ No benefit found with that ID')
      return
    }
    
    const benefit = benefitResult.rows[0]
    console.log('   ✅ Benefit found:')
    console.log(`   - Name: ${benefit.benefit_name}`)
    console.log(`   - Company: ${benefit.company_name}`)
    console.log(`   - Domain: ${benefit.company_domain}`)
    console.log(`   - Verified: ${benefit.is_verified}`)
    
    // Test dispute stats query
    console.log('\n2. Testing dispute stats query...')
    const disputeStatsResult = await client.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM benefit_removal_disputes
      WHERE company_benefit_id = $1
      GROUP BY status
    `, [companyBenefitId])
    
    console.log(`   ✅ Dispute stats query successful (${disputeStatsResult.rows.length} rows)`)
    disputeStatsResult.rows.forEach(row => {
      console.log(`   - ${row.status}: ${row.count}`)
    })
    
    // Test user dispute query (with a fake user ID)
    console.log('\n3. Testing user dispute query...')
    const fakeUserId = 'test-user-id'
    const userDisputeResult = await client.query(
      'SELECT * FROM benefit_removal_disputes WHERE company_benefit_id = $1 AND user_id = $2',
      [companyBenefitId, fakeUserId]
    )
    
    console.log(`   ✅ User dispute query successful (${userDisputeResult.rows.length} rows)`)
    
    // Test recent disputes query
    console.log('\n4. Testing recent disputes query...')
    const recentDisputesResult = await client.query(`
      SELECT 
        brd.id,
        brd.reason,
        brd.status,
        brd.created_at,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name
      FROM benefit_removal_disputes brd
      LEFT JOIN users u ON brd.user_id = u.id
      WHERE brd.company_benefit_id = $1
      ORDER BY brd.created_at DESC
      LIMIT 10
    `, [companyBenefitId])
    
    console.log(`   ✅ Recent disputes query successful (${recentDisputesResult.rows.length} rows)`)
    
    console.log('\n' + '=' .repeat(40))
    console.log('✅ All SQL queries work correctly!')
    console.log('   The issue must be elsewhere in the API.')
    
  } catch (error) {
    console.error('❌ SQL query failed:', error)
    console.error('Error details:', {
      code: error.code,
      detail: error.detail,
      hint: error.hint,
      position: error.position
    })
  } finally {
    client.release()
    await pool.end()
  }
}

testSQLQuery().catch(console.error)
