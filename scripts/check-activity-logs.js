// Check activity logs to see if dispute submissions are being logged
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkActivityLogs() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Activity Logs')
    console.log('=' .repeat(40))
    
    // Check if activity_log table exists
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'activity_log'
      );
    `)

    if (!tableExists.rows[0].exists) {
      console.log('❌ activity_log table does not exist!')
      
      // Check what tables do exist
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `)
      
      console.log('\nAvailable tables:')
      tables.rows.forEach(row => {
        console.log(`  - ${row.table_name}`)
      })
      return
    }
    
    console.log('✅ activity_log table exists')

    // Check recent activity logs
    const recentLogs = await client.query(`
      SELECT
        id,
        action,
        entity_type,
        entity_id,
        user_id,
        user_email,
        details,
        created_at
      FROM activity_log
      ORDER BY created_at DESC
      LIMIT 20
    `)
    
    console.log(`\nFound ${recentLogs.rows.length} recent activity logs:`)
    
    let disputeLogCount = 0
    
    recentLogs.rows.forEach((log, index) => {
      const isDisputeRelated = log.action.toLowerCase().includes('dispute') || 
                              log.entity_type === 'benefit_removal_dispute'
      
      if (isDisputeRelated) disputeLogCount++
      
      console.log(`\n${index + 1}. ${log.action} ${isDisputeRelated ? '🔥' : ''}`)
      console.log(`   Entity: ${log.entity_type} (${log.entity_id})`)
      console.log(`   User: ${log.user_email}`)
      console.log(`   Time: ${log.created_at}`)
      if (log.details) {
        console.log(`   Details: ${JSON.stringify(log.details).substring(0, 100)}...`)
      }
    })
    
    console.log(`\nDispute-related logs: ${disputeLogCount}`)
    
    // Check for specific dispute actions
    const disputeActions = await client.query(`
      SELECT action, COUNT(*) as count
      FROM activity_log
      WHERE action ILIKE '%dispute%' OR entity_type = 'benefit_removal_dispute'
      GROUP BY action
      ORDER BY count DESC
    `)
    
    if (disputeActions.rows.length > 0) {
      console.log('\nDispute action types:')
      disputeActions.rows.forEach(row => {
        console.log(`  - ${row.action}: ${row.count}`)
      })
    } else {
      console.log('\n❌ No dispute-related activity logs found!')
      console.log('   This confirms that dispute submissions are not being logged.')
    }
    
    // Check when the most recent disputes were created vs when they were logged
    console.log('\n' + '=' .repeat(40))
    console.log('Comparing dispute creation times with activity logs:')
    
    const disputes = await client.query(`
      SELECT id, created_at, reason
      FROM benefit_removal_disputes
      ORDER BY created_at DESC
      LIMIT 5
    `)
    
    console.log('\nRecent disputes:')
    disputes.rows.forEach((dispute, index) => {
      console.log(`${index + 1}. ${dispute.id}`)
      console.log(`   Created: ${dispute.created_at}`)
      console.log(`   Reason: ${dispute.reason}`)
    })
    
    // Check if these disputes have corresponding activity logs
    if (disputes.rows.length > 0) {
      const disputeIds = disputes.rows.map(d => d.id)
      const correspondingLogs = await client.query(`
        SELECT entity_id, action, created_at
        FROM activity_log
        WHERE entity_id = ANY($1) AND entity_type = 'benefit_removal_dispute'
        ORDER BY created_at DESC
      `, [disputeIds])
      
      console.log(`\nCorresponding activity logs: ${correspondingLogs.rows.length}`)
      correspondingLogs.rows.forEach(log => {
        console.log(`  - ${log.action} for ${log.entity_id} at ${log.created_at}`)
      })
      
      if (correspondingLogs.rows.length === 0) {
        console.log('❌ No activity logs found for recent disputes!')
        console.log('   Dispute submissions are definitely not being logged.')
      }
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkActivityLogs().catch(console.error)
