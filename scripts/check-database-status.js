// Check database status and data
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkDatabaseStatus() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Database Status')
    console.log('=' .repeat(40))
    
    // Check companies
    const companies = await client.query('SELECT COUNT(*) as count FROM companies')
    console.log(`Companies: ${companies.rows[0].count}`)
    
    // Check benefits
    const benefits = await client.query('SELECT COUNT(*) as count FROM benefits')
    console.log(`Benefits: ${benefits.rows[0].count}`)
    
    // Check company_benefits
    const companyBenefits = await client.query('SELECT COUNT(*) as count FROM company_benefits')
    console.log(`Company Benefits: ${companyBenefits.rows[0].count}`)
    
    // Check verified benefits
    const verifiedBenefits = await client.query('SELECT COUNT(*) as count FROM company_benefits WHERE is_verified = true')
    console.log(`Verified Benefits: ${verifiedBenefits.rows[0].count}`)
    
    // Check old disputes
    const oldDisputes = await client.query('SELECT COUNT(*) as count FROM benefit_verifications WHERE status = \'disputed\'')
    console.log(`Old Disputes: ${oldDisputes.rows[0].count}`)
    
    // Check new disputes
    const newDisputes = await client.query('SELECT COUNT(*) as count FROM benefit_removal_disputes')
    console.log(`New Disputes: ${newDisputes.rows[0].count}`)
    
    // If we have data, show some examples
    if (parseInt(companies.rows[0].count) > 0) {
      console.log('\n📊 Sample Data:')
      
      const sampleCompanies = await client.query('SELECT id, name, domain FROM companies LIMIT 3')
      console.log('Companies:')
      sampleCompanies.rows.forEach(c => {
        console.log(`  - ${c.name} (${c.domain})`)
      })
      
      if (parseInt(verifiedBenefits.rows[0].count) > 0) {
        const sampleVerified = await client.query(`
          SELECT cb.id, b.name, c.name as company_name, cb.is_verified, cb.added_by
          FROM company_benefits cb
          JOIN benefits b ON cb.benefit_id = b.id
          JOIN companies c ON cb.company_id = c.id
          WHERE cb.is_verified = true
          LIMIT 3
        `)
        console.log('\nVerified Benefits:')
        sampleVerified.rows.forEach(b => {
          console.log(`  - ${b.name} at ${b.company_name} (added_by: ${b.added_by || 'NULL'})`)
        })
      }
      
      if (parseInt(oldDisputes.rows[0].count) > 0) {
        const sampleDisputes = await client.query(`
          SELECT bv.id, bv.comment, b.name as benefit_name, c.name as company_name
          FROM benefit_verifications bv
          JOIN company_benefits cb ON bv.company_benefit_id = cb.id
          JOIN benefits b ON cb.benefit_id = b.id
          JOIN companies c ON cb.company_id = c.id
          WHERE bv.status = 'disputed'
          LIMIT 3
        `)
        console.log('\nOld Disputes:')
        sampleDisputes.rows.forEach(d => {
          console.log(`  - ${d.benefit_name} at ${d.company_name}: ${d.comment || 'No comment'}`)
        })
      }
    } else {
      console.log('\n⚠️  Database appears to be empty!')
      console.log('   This might explain why dispute buttons are not showing.')
      console.log('   The application needs sample data to test the dispute functionality.')
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkDatabaseStatus().catch(console.error)
