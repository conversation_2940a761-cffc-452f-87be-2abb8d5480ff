// Check the constraints on the activity_log table
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkActivityLogConstraints() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Activity Log Constraints')
    console.log('=' .repeat(40))
    
    // Check all constraints on the activity_log table
    const constraints = await client.query(`
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint
      WHERE conrelid = 'activity_log'::regclass
    `)
    
    console.log(`Found ${constraints.rows.length} constraints:`)
    
    constraints.rows.forEach((constraint, index) => {
      console.log(`\n${index + 1}. ${constraint.constraint_name}`)
      console.log(`   Type: ${constraint.constraint_type}`)
      console.log(`   Definition: ${constraint.constraint_definition}`)
      
      if (constraint.constraint_name === 'activity_log_event_type_check') {
        console.log('   🔥 This is the failing constraint!')
      }
    })
    
    // Check what event types currently exist in the table
    console.log('\n' + '=' .repeat(40))
    console.log('Current event types in activity_log:')
    
    const eventTypes = await client.query(`
      SELECT event_type, COUNT(*) as count
      FROM activity_log
      GROUP BY event_type
      ORDER BY count DESC
    `)
    
    console.log(`\nFound ${eventTypes.rows.length} different event types:`)
    eventTypes.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.event_type}: ${row.count} entries`)
    })
    
    // Check if the new event type is in the allowed list
    const newEventType = 'benefit_removal_dispute_submitted'
    const hasNewEventType = eventTypes.rows.some(row => row.event_type === newEventType)
    
    if (hasNewEventType) {
      console.log(`\n✅ Event type '${newEventType}' is already in use`)
    } else {
      console.log(`\n❌ Event type '${newEventType}' is NOT in the database`)
      console.log('   This confirms the constraint is blocking it')
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkActivityLogConstraints().catch(console.error)
