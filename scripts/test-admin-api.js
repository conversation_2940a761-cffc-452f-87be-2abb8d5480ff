// Test the admin API with a session token
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function testAdminAPI() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Testing Admin API Access')
    console.log('=' .repeat(40))
    
    // Get an active admin session token
    const adminSession = await client.query(`
      SELECT us.session_token, u.email, u.role
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE u.role = 'admin' 
      AND us.expires_at > NOW()
      ORDER BY us.expires_at DESC
      LIMIT 1
    `)
    
    if (adminSession.rows.length === 0) {
      console.log('❌ No active admin sessions found')
      console.log('   Please sign <NAME_EMAIL> first')
      return
    }
    
    const sessionToken = adminSession.rows[0].session_token
    const adminEmail = adminSession.rows[0].email
    
    console.log(`Found admin session for: ${adminEmail}`)
    console.log(`Session token: ${sessionToken.substring(0, 20)}...`)
    
    // Test the admin API with the session token
    console.log('\nTesting admin API...')
    
    const fetch = (await import('node-fetch')).default
    
    const response = await fetch('http://localhost:3002/api/admin/benefit-removal-disputes?status=pending', {
      headers: {
        'Cookie': `session_token=${sessionToken}`
      }
    })
    
    console.log(`Response status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Admin API working!')
      console.log(`Found ${data.disputes?.length || 0} disputes`)
      console.log(`Total: ${data.total}`)
      
      if (data.disputes && data.disputes.length > 0) {
        console.log('\nDisputes:')
        data.disputes.forEach((dispute, index) => {
          console.log(`${index + 1}. ${dispute.benefit_name} at ${dispute.company_name}`)
          console.log(`   Status: ${dispute.status}`)
          console.log(`   User: ${dispute.user_email}`)
          console.log(`   Reason: ${dispute.reason}`)
        })
      }
    } else {
      console.log('❌ Admin API failed')
      const errorText = await response.text()
      console.log(`Error: ${errorText}`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

testAdminAPI().catch(console.error)
