#!/usr/bin/env node

/**
 * Test script to verify UI behavior for dispute cancellation
 * This script tests that the UI correctly hides/shows sections based on dispute states
 */

const { Client } = require('pg')

async function testUIBehavior() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password'
  })

  try {
    await client.connect()
    console.log('🔗 Connected to database')

    // Find test data
    const userResult = await client.query(`
      SELECT id, email FROM users WHERE email LIKE '%@adidas.de' LIMIT 1
    `)
    
    if (userResult.rows.length === 0) {
      console.log('❌ No test user found')
      return
    }
    
    const user = userResult.rows[0]
    
    const benefitResult = await client.query(`
      SELECT cb.id as company_benefit_id, b.name as benefit_name, c.name as company_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE c.domain = 'adidas.de' AND cb.is_verified = true
      LIMIT 1
    `)
    
    if (benefitResult.rows.length === 0) {
      console.log('❌ No verified benefits found')
      return
    }
    
    const benefit = benefitResult.rows[0]
    
    // Create session
    const sessionResult = await client.query(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES ($1, $2, NOW() + INTERVAL '1 hour')
      RETURNING session_token
    `, [user.id, `ui_test_${Date.now()}`])
    
    const sessionToken = sessionResult.rows[0].session_token
    const fetch = (await import('node-fetch')).default

    console.log('\n📊 Testing UI behavior for different dispute states...')

    // Test 1: No disputes
    console.log('\n1. Testing with no disputes...')
    let response = await fetch(`http://localhost:3001/api/benefit-removal-disputes/${benefit.company_benefit_id}`, {
      headers: { 'Cookie': `session_token=${sessionToken}` }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log(`   Stats: P:${data.stats.pending} A:${data.stats.approved} R:${data.stats.rejected}`)
      console.log(`   User dispute: ${data.userDispute ? data.userDispute.status : 'none'}`)
      console.log(`   Can dispute: ${data.canDispute}`)
      
      const hasActiveDisputes = data.stats.pending > 0 || data.stats.approved > 0 || data.stats.rejected > 0
      const hasActiveUserDispute = data.userDispute && data.userDispute.status !== 'cancelled'
      
      console.log(`   UI Logic: hasActiveDisputes=${hasActiveDisputes}, hasActiveUserDispute=${hasActiveUserDispute}`)
      console.log(`   Expected: Dispute sections should be HIDDEN, Request button should be SHOWN`)
    }

    // Test 2: Submit a dispute
    console.log('\n2. Testing with pending dispute...')
    const submitResponse = await fetch('http://localhost:3001/api/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${sessionToken}`
      },
      body: JSON.stringify({
        companyBenefitId: benefit.company_benefit_id,
        reason: 'UI test dispute'
      })
    })
    
    if (submitResponse.ok) {
      const submitData = await submitResponse.json()
      const disputeId = submitData.dispute.id
      
      response = await fetch(`http://localhost:3001/api/benefit-removal-disputes/${benefit.company_benefit_id}`, {
        headers: { 'Cookie': `session_token=${sessionToken}` }
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`   Stats: P:${data.stats.pending} A:${data.stats.approved} R:${data.stats.rejected}`)
        console.log(`   User dispute: ${data.userDispute ? data.userDispute.status : 'none'}`)
        console.log(`   Can dispute: ${data.canDispute}`)
        
        const hasActiveDisputes = data.stats.pending > 0 || data.stats.approved > 0 || data.stats.rejected > 0
        const hasActiveUserDispute = data.userDispute && data.userDispute.status !== 'cancelled'
        
        console.log(`   UI Logic: hasActiveDisputes=${hasActiveDisputes}, hasActiveUserDispute=${hasActiveUserDispute}`)
        console.log(`   Expected: Dispute sections should be SHOWN, Request button should be HIDDEN`)
      }

      // Test 3: Cancel the dispute
      console.log('\n3. Testing after cancelling dispute...')
      const cancelResponse = await fetch('http://localhost:3001/api/benefit-removal-disputes/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session_token=${sessionToken}`
        },
        body: JSON.stringify({ disputeId })
      })
      
      if (cancelResponse.ok) {
        response = await fetch(`http://localhost:3001/api/benefit-removal-disputes/${benefit.company_benefit_id}`, {
          headers: { 'Cookie': `session_token=${sessionToken}` }
        })
        
        if (response.ok) {
          const data = await response.json()
          console.log(`   Stats: P:${data.stats.pending} A:${data.stats.approved} R:${data.stats.rejected}`)
          console.log(`   User dispute: ${data.userDispute ? data.userDispute.status : 'none'}`)
          console.log(`   Can dispute: ${data.canDispute}`)
          
          const hasActiveDisputes = data.stats.pending > 0 || data.stats.approved > 0 || data.stats.rejected > 0
          const hasActiveUserDispute = data.userDispute && data.userDispute.status !== 'cancelled'
          
          console.log(`   UI Logic: hasActiveDisputes=${hasActiveDisputes}, hasActiveUserDispute=${hasActiveUserDispute}`)
          console.log(`   Expected: Dispute sections should be HIDDEN, Request button should be SHOWN`)
          
          if (!hasActiveDisputes && !hasActiveUserDispute && data.canDispute) {
            console.log(`   ✅ UI behavior is correct after cancellation!`)
          } else {
            console.log(`   ❌ UI behavior is incorrect after cancellation`)
          }
        }
      }
      
      // Clean up
      await client.query('DELETE FROM benefit_removal_disputes WHERE id = $1', [disputeId])
    }

    // Clean up session
    await client.query('DELETE FROM user_sessions WHERE session_token = $1', [sessionToken])
    
    console.log('\n🎉 UI behavior test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await client.end()
  }
}

if (require.main === module) {
  testUIBehavior()
}
