// Check what user_id values are stored in benefit_removal_disputes
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkDisputeUserIds() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Dispute User IDs')
    console.log('=' .repeat(40))
    
    // Check what user_id values are in the disputes table
    const disputes = await client.query(`
      SELECT user_id, reason, created_at
      FROM benefit_removal_disputes
      ORDER BY created_at DESC
    `)
    
    console.log(`Found ${disputes.rows.length} disputes:`)
    disputes.rows.forEach((dispute, index) => {
      console.log(`${index + 1}. User ID: ${dispute.user_id}`)
      console.log(`   Type: ${typeof dispute.user_id}`)
      console.log(`   Length: ${dispute.user_id.length}`)
      console.log(`   Looks like UUID: ${/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(dispute.user_id)}`)
      console.log(`   Reason: ${dispute.reason.substring(0, 50)}...`)
      console.log('')
    })
    
    // Check if these IDs exist in users table
    if (disputes.rows.length > 0) {
      console.log('Checking if these user IDs exist in users table...')
      
      for (const dispute of disputes.rows) {
        const userCheck = await client.query(
          'SELECT id, email FROM users WHERE id = $1',
          [dispute.user_id]
        )
        
        if (userCheck.rows.length > 0) {
          console.log(`✅ User ID ${dispute.user_id} found in users table: ${userCheck.rows[0].email}`)
        } else {
          console.log(`❌ User ID ${dispute.user_id} NOT found in users table`)
          
          // Check if it's a session ID
          const sessionCheck = await client.query(
            'SELECT id, user_id FROM user_sessions WHERE id = $1',
            [dispute.user_id]
          )
          
          if (sessionCheck.rows.length > 0) {
            console.log(`   → This is a session ID, actual user_id: ${sessionCheck.rows[0].user_id}`)
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkDisputeUserIds().catch(console.error)
