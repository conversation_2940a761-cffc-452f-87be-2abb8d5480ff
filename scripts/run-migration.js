const { Pool } = require('pg')
const fs = require('fs')
const path = require('path')

// Use environment variable directly
const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell:workwell@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function runMigration() {
  const client = await pool.connect()
  
  try {
    console.log('Running benefit removal disputes migration...')
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '011-add-benefit-removal-disputes.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Execute the migration
    await client.query(migrationSQL)
    
    console.log('Migration completed successfully!')
    
  } catch (error) {
    console.error('Migration failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

runMigration().catch(console.error)
