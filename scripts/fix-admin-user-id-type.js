// Fix the admin_user_id column type in benefit_removal_disputes table
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function fixAdminUserIdType() {
  const client = await pool.connect()
  
  try {
    console.log('🔧 Fixing admin_user_id Column Type')
    console.log('=' .repeat(40))
    
    await client.query('BEGIN')
    
    // 1. Check current type
    console.log('\n1. Checking current column type...')
    const currentType = await client.query(`
      SELECT data_type 
      FROM information_schema.columns 
      WHERE table_name = 'benefit_removal_disputes' 
      AND column_name = 'admin_user_id'
    `)
    
    console.log(`   Current type: ${currentType.rows[0]?.data_type || 'unknown'}`)
    
    // 2. Check if there's data that would be affected
    console.log('\n2. Checking existing admin_user_id data...')
    const dataCheck = await client.query(`
      SELECT admin_user_id, COUNT(*) as count
      FROM benefit_removal_disputes
      WHERE admin_user_id IS NOT NULL
      GROUP BY admin_user_id
    `)
    
    console.log(`   Found ${dataCheck.rows.length} unique admin user IDs:`)
    if (dataCheck.rows.length === 0) {
      console.log('   - No admin_user_id values set (all NULL)')
    } else {
      dataCheck.rows.forEach(row => {
        const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(row.admin_user_id)
        console.log(`   - ${row.admin_user_id} (${row.count} disputes) - Valid UUID: ${isValidUuid}`)
      })
    }
    
    // 3. Alter the column type
    console.log('\n3. Altering admin_user_id column type from VARCHAR to UUID...')
    
    try {
      // PostgreSQL can automatically convert VARCHAR to UUID if the values are valid UUIDs
      // For NULL values, this should work fine
      await client.query(`
        ALTER TABLE benefit_removal_disputes 
        ALTER COLUMN admin_user_id TYPE UUID USING 
        CASE 
          WHEN admin_user_id IS NULL THEN NULL
          ELSE admin_user_id::UUID
        END
      `)
      
      console.log('   ✅ Column type changed successfully')
      
      // 4. Verify the change
      console.log('\n4. Verifying the change...')
      const newType = await client.query(`
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'benefit_removal_disputes' 
        AND column_name = 'admin_user_id'
      `)
      
      console.log(`   New type: ${newType.rows[0]?.data_type || 'unknown'}`)
      
      // 5. Test the problematic join
      console.log('\n5. Testing the admin join...')
      const testJoin = await client.query(`
        SELECT 
          brd.id,
          brd.reason,
          brd.status,
          u.email as user_email,
          admin_u.email as admin_email
        FROM benefit_removal_disputes brd
        LEFT JOIN users u ON brd.user_id = u.id
        LEFT JOIN users admin_u ON brd.admin_user_id = admin_u.id
        LIMIT 5
      `)
      
      console.log(`   ✅ Admin join test successful (${testJoin.rows.length} rows)`)
      testJoin.rows.forEach(row => {
        console.log(`   - User: ${row.user_email}, Admin: ${row.admin_email || 'None'}`)
      })
      
      await client.query('COMMIT')
      
      console.log('\n' + '=' .repeat(40))
      console.log('✅ admin_user_id column type fixed successfully!')
      console.log('   • Changed from VARCHAR(255) to UUID')
      console.log('   • All existing data preserved')
      console.log('   • Join with users table now works')
      console.log('   • Admin API should now work correctly')
      
    } catch (alterError) {
      await client.query('ROLLBACK')
      console.error('❌ Failed to alter column type:', alterError.message)
      
      if (alterError.message.includes('invalid input syntax for type uuid')) {
        console.log('\n💡 Some admin_user_id values are not valid UUIDs.')
        console.log('   Manual data cleanup may be required.')
      }
      
      throw alterError
    }
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Fix failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

fixAdminUserIdType().catch(console.error)
