// Check what disputes exist in the database
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkDisputesInDB() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Disputes in Database')
    console.log('=' .repeat(50))
    
    // Check all disputes in the table
    const disputes = await client.query(`
      SELECT 
        brd.id,
        brd.user_id,
        brd.reason,
        brd.status,
        brd.created_at,
        brd.updated_at,
        cb.id as company_benefit_id,
        b.name as benefit_name,
        c.name as company_name,
        c.domain as company_domain,
        u.email as user_email,
        u.first_name,
        u.last_name
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      LEFT JOIN users u ON brd.user_id = u.id
      ORDER BY brd.created_at DESC
    `)
    
    console.log(`\nFound ${disputes.rows.length} disputes in database:`)
    
    disputes.rows.forEach((dispute, index) => {
      console.log(`\n${index + 1}. Dispute ID: ${dispute.id}`)
      console.log(`   Benefit: ${dispute.benefit_name}`)
      console.log(`   Company: ${dispute.company_name} (${dispute.company_domain})`)
      console.log(`   User: ${dispute.user_email || 'Unknown'} (${dispute.first_name} ${dispute.last_name})`)
      console.log(`   Status: ${dispute.status}`)
      console.log(`   Reason: ${dispute.reason}`)
      console.log(`   Created: ${dispute.created_at}`)
      console.log(`   Updated: ${dispute.updated_at}`)
    })
    
    // Check if there are any issues with the data
    console.log('\n' + '=' .repeat(50))
    console.log('Data Quality Check:')
    
    const statusCounts = await client.query(`
      SELECT status, COUNT(*) as count
      FROM benefit_removal_disputes
      GROUP BY status
    `)
    
    console.log('\nDispute status distribution:')
    statusCounts.rows.forEach(row => {
      console.log(`  - ${row.status}: ${row.count}`)
    })
    
    // Check for any NULL values that might cause issues
    const nullChecks = await client.query(`
      SELECT 
        COUNT(CASE WHEN user_id IS NULL THEN 1 END) as null_user_ids,
        COUNT(CASE WHEN company_benefit_id IS NULL THEN 1 END) as null_benefit_ids,
        COUNT(CASE WHEN reason IS NULL OR reason = '' THEN 1 END) as empty_reasons,
        COUNT(CASE WHEN status IS NULL THEN 1 END) as null_status
      FROM benefit_removal_disputes
    `)
    
    const nulls = nullChecks.rows[0]
    console.log('\nData integrity check:')
    console.log(`  - NULL user_ids: ${nulls.null_user_ids}`)
    console.log(`  - NULL benefit_ids: ${nulls.null_benefit_ids}`)
    console.log(`  - Empty reasons: ${nulls.empty_reasons}`)
    console.log(`  - NULL status: ${nulls.null_status}`)
    
    if (disputes.rows.length > 0) {
      console.log('\n✅ Disputes exist in database')
      console.log('   If they\'re not showing in admin panel, the issue is in the API or UI')
    } else {
      console.log('\n❌ No disputes found in database')
      console.log('   This explains why admin panel is empty')
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkDisputesInDB().catch(console.error)
