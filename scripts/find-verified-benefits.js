// Find companies with verified benefits to test dispute buttons
const BASE_URL = 'http://localhost:3002'

async function findVerifiedBenefits() {
  console.log('🔍 Finding Companies with Verified Benefits')
  console.log('=' .repeat(50))
  
  try {
    const companiesResponse = await fetch(`${BASE_URL}/api/companies`)
    const companies = await companiesResponse.json()
    
    console.log(`Found ${companies.length} companies total`)
    
    let foundVerified = false
    
    for (let i = 0; i < Math.min(companies.length, 10); i++) {
      const company = companies[i]
      console.log(`\n${i + 1}. Checking ${company.name}...`)
      
      const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${company.id}/benefits`)
      if (benefitsResponse.ok) {
        const benefits = await benefitsResponse.json()
        const verifiedBenefits = benefits.filter(b => b.is_verified)
        const adminVerifiedBenefits = benefits.filter(b => b.is_admin_verified)
        
        console.log(`   Total benefits: ${benefits.length}`)
        console.log(`   Verified benefits: ${verifiedBenefits.length}`)
        console.log(`   Admin-verified benefits: ${adminVerifiedBenefits.length}`)
        
        if (verifiedBenefits.length > 0) {
          foundVerified = true
          console.log(`   ✅ Found verified benefits!`)
          
          verifiedBenefits.slice(0, 3).forEach(benefit => {
            console.log(`   - ${benefit.name} (verified: ${benefit.is_verified}, admin: ${benefit.is_admin_verified})`)
          })
          
          // Test dispute API for first verified benefit
          const testBenefit = verifiedBenefits[0]
          console.log(`\n   Testing dispute API for "${testBenefit.name}"...`)
          
          const disputeResponse = await fetch(`${BASE_URL}/api/benefit-removal-disputes/${testBenefit.id}`)
          console.log(`   Status: ${disputeResponse.status}`)
          
          if (disputeResponse.status === 401) {
            console.log(`   ⚠️  Authentication required - this is why dispute buttons don't show`)
            console.log(`   ✅ This confirms the system is working correctly`)
            console.log(`   📝 Users need to sign in to see dispute buttons`)
          } else if (disputeResponse.ok) {
            const disputeData = await disputeResponse.json()
            console.log(`   Can dispute: ${disputeData.canDispute}`)
            console.log(`   User dispute: ${disputeData.userDispute ? 'Yes' : 'No'}`)
          }
          
          break // Found what we need
        }
      }
    }
    
    if (!foundVerified) {
      console.log('\n⚠️  No verified benefits found in first 10 companies')
      console.log('   Let me check the database directly...')
      
      // Check database for verified benefits
      const { Pool } = require('pg')
      const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'
      
      const pool = new Pool({ connectionString: DATABASE_URL })
      const client = await pool.connect()
      
      try {
        const verifiedInDB = await client.query(`
          SELECT c.name as company_name, b.name as benefit_name, cb.is_verified, cb.added_by
          FROM company_benefits cb
          JOIN companies c ON cb.company_id = c.id
          JOIN benefits b ON cb.benefit_id = b.id
          WHERE cb.is_verified = true
          LIMIT 5
        `)
        
        console.log(`\n   Database shows ${verifiedInDB.rows.length} verified benefits:`)
        verifiedInDB.rows.forEach(row => {
          console.log(`   - ${row.benefit_name} at ${row.company_name} (added_by: ${row.added_by || 'NULL'})`)
        })
        
      } finally {
        client.release()
        await pool.end()
      }
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log('📋 Summary:')
    if (foundVerified) {
      console.log('✅ Found companies with verified benefits')
      console.log('✅ Dispute API requires authentication (correct behavior)')
      console.log('📝 To see dispute buttons: Sign in with company email')
    } else {
      console.log('⚠️  API might not be returning verified benefits correctly')
    }
    
  } catch (error) {
    console.error('❌ Search failed:', error.message)
  }
}

findVerifiedBenefits()
