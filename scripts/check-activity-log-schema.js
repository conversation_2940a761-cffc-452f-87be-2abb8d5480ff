// Check the actual schema of the activity_log table
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkActivityLogSchema() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Activity Log Schema')
    console.log('=' .repeat(40))
    
    // Check the schema of activity_log table
    const schema = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'activity_log'
      ORDER BY ordinal_position
    `)
    
    console.log('activity_log table schema:')
    schema.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`)
    })
    
    // Check sample data
    const sampleData = await client.query(`
      SELECT * FROM activity_log ORDER BY created_at DESC LIMIT 5
    `)
    
    console.log(`\nSample data (${sampleData.rows.length} rows):`)
    sampleData.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${JSON.stringify(row, null, 2)}`)
    })
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkActivityLogSchema().catch(console.error)
