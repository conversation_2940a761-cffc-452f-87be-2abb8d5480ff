// Test script to verify the new benefit removal dispute system
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function testNewDisputeSystem() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Testing New Benefit Removal Dispute System')
    console.log('=' .repeat(50))
    
    // Test 1: Check if the new table exists
    console.log('\n1. Checking if benefit_removal_disputes table exists...')
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'benefit_removal_disputes'
      );
    `)
    
    if (tableCheck.rows[0].exists) {
      console.log('✅ benefit_removal_disputes table exists')
    } else {
      console.log('❌ benefit_removal_disputes table does not exist')
      return
    }
    
    // Test 2: Check table structure
    console.log('\n2. Checking table structure...')
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'benefit_removal_disputes' 
      ORDER BY ordinal_position;
    `)
    
    console.log('   Table columns:')
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`)
    })
    
    // Test 3: Check existing disputes in the new table
    console.log('\n3. Checking existing removal disputes...')
    const removalDisputes = await client.query(`
      SELECT COUNT(*) as count FROM benefit_removal_disputes
    `)
    console.log(`   Found ${removalDisputes.rows[0].count} removal disputes`)
    
    // Test 4: Check old verification disputes
    console.log('\n4. Checking old verification disputes...')
    const verificationDisputes = await client.query(`
      SELECT 
        bv.id,
        bv.status,
        bv.comment,
        bv.created_at,
        b.name as benefit_name,
        c.name as company_name
      FROM benefit_verifications bv
      JOIN company_benefits cb ON bv.company_benefit_id = cb.id
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE bv.status = 'disputed'
      ORDER BY bv.created_at DESC
      LIMIT 5
    `)
    
    console.log(`   Found ${verificationDisputes.rows.length} verification disputes:`)
    verificationDisputes.rows.forEach(dispute => {
      console.log(`   - ${dispute.benefit_name} at ${dispute.company_name} (${dispute.created_at})`)
      if (dispute.comment) {
        console.log(`     Comment: ${dispute.comment}`)
      }
    })
    
    // Test 5: Check verified benefits that could be disputed for removal
    console.log('\n5. Checking verified benefits available for removal disputes...')
    const verifiedBenefits = await client.query(`
      SELECT 
        cb.id,
        b.name as benefit_name,
        c.name as company_name,
        c.domain
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE cb.is_verified = true
      ORDER BY c.name, b.name
      LIMIT 10
    `)
    
    console.log(`   Found ${verifiedBenefits.rows.length} verified benefits:`)
    verifiedBenefits.rows.forEach(benefit => {
      console.log(`   - ${benefit.benefit_name} at ${benefit.company_name} (${benefit.domain})`)
    })
    
    console.log('\n' + '=' .repeat(50))
    console.log('📋 Summary:')
    console.log(`   • New dispute system: ✅ Ready`)
    console.log(`   • Removal disputes: ${removalDisputes.rows[0].count}`)
    console.log(`   • Old verification disputes: ${verificationDisputes.rows.length}`)
    console.log(`   • Verified benefits available: ${verifiedBenefits.rows.length}`)
    
    if (verificationDisputes.rows.length > 0) {
      console.log('\n💡 Note: The old verification disputes are separate from removal disputes.')
      console.log('   Verification disputes question if a benefit exists.')
      console.log('   Removal disputes request removal of verified benefits.')
      console.log('   Both systems can coexist.')
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

runTest().catch(console.error)

async function runTest() {
  await testNewDisputeSystem()
}
