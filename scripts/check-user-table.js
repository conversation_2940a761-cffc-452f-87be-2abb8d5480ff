// Check the users table schema
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkUserTable() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking User Table Schema')
    console.log('=' .repeat(40))
    
    // Check if users table exists and its schema
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `)
    
    if (tableInfo.rows.length === 0) {
      console.log('❌ Users table does not exist!')
      
      // Check what tables do exist
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `)
      
      console.log('\nAvailable tables:')
      tables.rows.forEach(row => {
        console.log(`  - ${row.table_name}`)
      })
      
    } else {
      console.log('✅ Users table schema:')
      tableInfo.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`)
      })
      
      // Check sample data
      const sampleUsers = await client.query('SELECT id, email FROM users LIMIT 3')
      console.log('\nSample users:')
      sampleUsers.rows.forEach(user => {
        console.log(`  - ID: ${user.id} (type: ${typeof user.id})`)
        console.log(`    Email: ${user.email}`)
      })
    }
    
    // Check user_sessions table
    console.log('\n' + '=' .repeat(40))
    console.log('Checking user_sessions table...')
    
    const sessionInfo = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'user_sessions'
      ORDER BY ordinal_position
    `)
    
    if (sessionInfo.rows.length === 0) {
      console.log('❌ user_sessions table does not exist!')
    } else {
      console.log('✅ user_sessions table schema:')
      sessionInfo.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`)
      })
      
      // Check sample data
      const sampleSessions = await client.query('SELECT id, email FROM user_sessions LIMIT 3')
      console.log('\nSample sessions:')
      sampleSessions.rows.forEach(session => {
        console.log(`  - ID: ${session.id} (type: ${typeof session.id})`)
        console.log(`    Email: ${session.email}`)
      })
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkUserTable().catch(console.error)
