// Create a test user and submit a dispute to test activity logging
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function createTestUserAndDispute() {
  const client = await pool.connect()
  
  try {
    console.log('🧪 Creating Test User and Dispute')
    console.log('=' .repeat(40))
    
    await client.query('BEGIN')
    
    // Find a verified benefit that hasn't been disputed yet
    const availableBenefit = await client.query(`
      SELECT 
        cb.id,
        b.name as benefit_name,
        c.name as company_name,
        c.domain
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      JOIN companies c ON cb.company_id = c.id
      WHERE cb.is_verified = true
      AND cb.id NOT IN (
        SELECT DISTINCT company_benefit_id 
        FROM benefit_removal_disputes
      )
      LIMIT 1
    `)
    
    if (availableBenefit.rows.length === 0) {
      console.log('❌ No available benefits to dispute')
      return
    }
    
    const benefit = availableBenefit.rows[0]
    console.log(`Found benefit: ${benefit.benefit_name} at ${benefit.company_name} (${benefit.domain})`)
    
    // Create a test user for this company domain
    const testEmail = `test.user@${benefit.domain}`

    // Generate a proper UUID for the user
    const { randomUUID } = require('crypto')
    const testUserId = randomUUID()
    
    console.log(`Creating test user: ${testEmail}`)
    
    // Check if user already exists
    const existingUser = await client.query('SELECT id FROM users WHERE email = $1', [testEmail])
    
    let userId
    if (existingUser.rows.length > 0) {
      userId = existingUser.rows[0].id
      console.log('User already exists, using existing user')
    } else {
      // Create test user
      const userResult = await client.query(`
        INSERT INTO users (id, email, first_name, last_name, role)
        VALUES ($1, $2, 'Test', 'User', 'user')
        RETURNING id
      `, [testUserId, testEmail])
      
      userId = userResult.rows[0].id
      console.log('Test user created')
    }
    
    // Create a session for the test user
    const sessionId = randomUUID()
    const sessionToken = 'test-session-' + Date.now()
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now

    await client.query(`
      INSERT INTO user_sessions (id, user_id, session_token, expires_at)
      VALUES ($1, $2, $3, $4)
    `, [sessionId, userId, sessionToken, expiresAt])
    
    console.log('Test session created')
    
    await client.query('COMMIT')
    
    // Count activity logs before submission
    const logsBefore = await client.query(`
      SELECT COUNT(*) as count FROM activity_log
      WHERE event_type = 'benefit_removal_dispute_submitted'
    `)
    
    console.log(`Activity logs before: ${logsBefore.rows[0].count}`)
    
    // Submit dispute via API
    console.log('\nSubmitting dispute via API...')
    
    const fetch = (await import('node-fetch')).default
    
    const response = await fetch('http://localhost:3002/api/benefit-removal-disputes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${sessionToken}`
      },
      body: JSON.stringify({
        companyBenefitId: benefit.id,
        reason: 'Test dispute to verify activity logging is working after database fixes'
      })
    })
    
    console.log(`Response status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Dispute submitted successfully')
      console.log(`Dispute ID: ${data.dispute.id}`)
      
      // Wait for logging to complete
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Count activity logs after submission
      const logsAfter = await client.query(`
        SELECT COUNT(*) as count FROM activity_log
        WHERE event_type = 'benefit_removal_dispute_submitted'
      `)
      
      console.log(`Activity logs after: ${logsAfter.rows[0].count}`)
      
      if (parseInt(logsAfter.rows[0].count) > parseInt(logsBefore.rows[0].count)) {
        console.log('✅ Activity logging is working!')
        
        // Show the new log entry
        const newLog = await client.query(`
          SELECT 
            event_type,
            event_description,
            user_email,
            benefit_name,
            company_name,
            created_at,
            metadata
          FROM activity_log
          WHERE event_type = 'benefit_removal_dispute_submitted'
          ORDER BY created_at DESC
          LIMIT 1
        `)
        
        if (newLog.rows.length > 0) {
          const log = newLog.rows[0]
          console.log('\n📋 New activity log entry:')
          console.log(`  Event: ${log.event_type}`)
          console.log(`  Description: ${log.event_description}`)
          console.log(`  User: ${log.user_email}`)
          console.log(`  Benefit: ${log.benefit_name} at ${log.company_name}`)
          console.log(`  Time: ${log.created_at}`)
          console.log(`  Metadata: ${JSON.stringify(log.metadata)}`)
        }
      } else {
        console.log('❌ Activity logging is NOT working!')
      }
      
      // Clean up test data
      console.log('\n🧹 Cleaning up test data...')
      await client.query('BEGIN')
      await client.query('DELETE FROM benefit_removal_disputes WHERE id = $1', [data.dispute.id])
      await client.query('DELETE FROM user_sessions WHERE session_token = $1', [sessionToken])
      // Always delete test user since we generated a UUID
      await client.query('DELETE FROM users WHERE id = $1', [userId])
      await client.query('COMMIT')
      console.log('Test data cleaned up')
      
    } else {
      const errorText = await response.text()
      console.log('❌ Dispute submission failed')
      console.log(`Error: ${errorText}`)
      
      // Clean up test user if created
      await client.query('BEGIN')
      await client.query('DELETE FROM user_sessions WHERE session_token = $1', [sessionToken])
      // Always delete test user since we generated a UUID
      await client.query('DELETE FROM users WHERE id = $1', [userId])
      await client.query('COMMIT')
    }
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Test failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

createTestUserAndDispute().catch(console.error)
