// Check what admin users exist in the system
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function checkAdminUsers() {
  const client = await pool.connect()
  
  try {
    console.log('🔍 Checking Admin Users')
    console.log('=' .repeat(40))
    
    // Check all users and their roles
    const users = await client.query(`
      SELECT id, email, first_name, last_name, role, created_at
      FROM users
      ORDER BY created_at DESC
    `)
    
    console.log(`\nFound ${users.rows.length} users:`)
    
    let adminCount = 0
    users.rows.forEach((user, index) => {
      const isAdmin = user.role === 'admin'
      if (isAdmin) adminCount++
      
      console.log(`${index + 1}. ${user.email}`)
      console.log(`   Name: ${user.first_name || 'N/A'} ${user.last_name || 'N/A'}`)
      console.log(`   Role: ${user.role || 'N/A'} ${isAdmin ? '👑 ADMIN' : ''}`)
      console.log(`   Created: ${user.created_at}`)
      console.log('')
    })
    
    console.log(`Admin users found: ${adminCount}`)
    
    if (adminCount === 0) {
      console.log('\n⚠️  No admin users found!')
      console.log('   This explains why admin panel access is failing.')
      console.log('   You need to sign in as an admin user to see disputes.')
    } else {
      console.log('\n✅ Admin users exist')
      console.log('   Make sure you\'re signed in as one of the admin users above.')
    }
    
    // Check current sessions
    console.log('\n' + '=' .repeat(40))
    console.log('Current active sessions:')
    
    const sessions = await client.query(`
      SELECT 
        us.id,
        us.user_id,
        us.expires_at,
        u.email,
        u.role
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE us.expires_at > NOW()
      ORDER BY us.expires_at DESC
    `)
    
    console.log(`\nFound ${sessions.rows.length} active sessions:`)
    sessions.rows.forEach((session, index) => {
      const isAdmin = session.role === 'admin'
      console.log(`${index + 1}. ${session.email} ${isAdmin ? '👑' : ''}`)
      console.log(`   Session expires: ${session.expires_at}`)
      console.log('')
    })
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

checkAdminUsers().catch(console.error)
