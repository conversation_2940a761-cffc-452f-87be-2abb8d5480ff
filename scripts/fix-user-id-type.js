// Fix the user_id column type in benefit_removal_disputes table
const { Pool } = require('pg')

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function fixUserIdType() {
  const client = await pool.connect()
  
  try {
    console.log('🔧 Fixing user_id Column Type')
    console.log('=' .repeat(40))
    
    await client.query('BEGIN')
    
    // 1. Check current type
    console.log('\n1. Checking current column type...')
    const currentType = await client.query(`
      SELECT data_type 
      FROM information_schema.columns 
      WHERE table_name = 'benefit_removal_disputes' 
      AND column_name = 'user_id'
    `)
    
    console.log(`   Current type: ${currentType.rows[0]?.data_type || 'unknown'}`)
    
    // 2. Check if there's data that would be affected
    console.log('\n2. Checking existing data...')
    const dataCheck = await client.query(`
      SELECT user_id, COUNT(*) as count
      FROM benefit_removal_disputes
      GROUP BY user_id
    `)
    
    console.log(`   Found ${dataCheck.rows.length} unique user IDs:`)
    dataCheck.rows.forEach(row => {
      const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(row.user_id)
      console.log(`   - ${row.user_id} (${row.count} disputes) - Valid UUID: ${isValidUuid}`)
    })
    
    // 3. Alter the column type
    console.log('\n3. Altering column type from VARCHAR to UUID...')
    
    try {
      // PostgreSQL can automatically convert VARCHAR to UUID if the values are valid UUIDs
      await client.query(`
        ALTER TABLE benefit_removal_disputes 
        ALTER COLUMN user_id TYPE UUID USING user_id::UUID
      `)
      
      console.log('   ✅ Column type changed successfully')
      
      // 4. Verify the change
      console.log('\n4. Verifying the change...')
      const newType = await client.query(`
        SELECT data_type 
        FROM information_schema.columns 
        WHERE table_name = 'benefit_removal_disputes' 
        AND column_name = 'user_id'
      `)
      
      console.log(`   New type: ${newType.rows[0]?.data_type || 'unknown'}`)
      
      // 5. Test the join that was failing
      console.log('\n5. Testing the problematic join...')
      const testJoin = await client.query(`
        SELECT 
          brd.id,
          brd.reason,
          brd.status,
          u.email as user_email,
          u.first_name as user_first_name,
          u.last_name as user_last_name
        FROM benefit_removal_disputes brd
        LEFT JOIN users u ON brd.user_id = u.id
        LIMIT 5
      `)
      
      console.log(`   ✅ Join test successful (${testJoin.rows.length} rows)`)
      testJoin.rows.forEach(row => {
        console.log(`   - ${row.user_email}: ${row.reason.substring(0, 30)}...`)
      })
      
      await client.query('COMMIT')
      
      console.log('\n' + '=' .repeat(40))
      console.log('✅ user_id column type fixed successfully!')
      console.log('   • Changed from VARCHAR(255) to UUID')
      console.log('   • All existing data preserved')
      console.log('   • Join with users table now works')
      
    } catch (alterError) {
      await client.query('ROLLBACK')
      console.error('❌ Failed to alter column type:', alterError.message)
      
      if (alterError.message.includes('invalid input syntax for type uuid')) {
        console.log('\n💡 Some user_id values are not valid UUIDs.')
        console.log('   Manual data cleanup may be required.')
      }
      
      throw alterError
    }
    
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('❌ Fix failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

fixUserIdType().catch(console.error)
