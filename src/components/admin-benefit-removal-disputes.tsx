'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Clock, AlertTriangle, Eye, MessageSquare } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Dispute {
  dispute_id: string
  company_benefit_id: string
  user_id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  admin_comment?: string
  created_at: string
  updated_at: string
  company_id: string
  company_name: string
  company_domain: string
  benefit_id: string
  benefit_name: string
  benefit_category: string
  benefit_icon?: string
  user_email: string
  user_first_name?: string
  user_last_name?: string
  admin_email?: string
  admin_first_name?: string
  admin_last_name?: string
  approved_disputes_count: number
  pending_disputes_count: number
  total_disputes_count: number
}

export function AdminBenefitRemovalDisputes() {
  const [disputes, setDisputes] = useState<Dispute[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<'pending' | 'approved' | 'rejected'>('pending')
  const [processingDispute, setProcessingDispute] = useState<string | null>(null)
  const [showCommentModal, setShowCommentModal] = useState<string | null>(null)
  const [adminComment, setAdminComment] = useState('')

  useEffect(() => {
    fetchDisputes()
  }, [selectedStatus])

  const fetchDisputes = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/benefit-removal-disputes?status=${selectedStatus}&limit=50`)

      if (response.ok) {
        const data = await response.json()
        setDisputes(data.disputes)
      } else {
        console.error('Failed to fetch disputes:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching disputes:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProcessDispute = async (disputeId: string, action: 'approve' | 'reject') => {
    if (!adminComment.trim() && action === 'reject') {
      alert('Please provide a comment when rejecting a dispute')
      return
    }

    setProcessingDispute(disputeId)
    try {
      const response = await fetch('/api/admin/benefit-removal-disputes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          disputeId,
          action,
          adminComment: adminComment.trim() || undefined
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        setShowCommentModal(null)
        setAdminComment('')
        fetchDisputes()
      } else {
        const error = await response.json()
        alert(error.error || `Failed to ${action} dispute`)
      }
    } catch (error) {
      console.error(`Error ${action}ing dispute:`, error)
      alert(`Failed to ${action} dispute`)
    } finally {
      setProcessingDispute(null)
    }
  }

  const openCommentModal = (disputeId: string) => {
    setShowCommentModal(disputeId)
    setAdminComment('')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-600" />
      default:
        return <Clock className="w-4 h-4 text-yellow-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-50 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-50 text-red-800 border-red-200'
      case 'cancelled':
        return 'bg-gray-50 text-gray-800 border-gray-200'
      default:
        return 'bg-yellow-50 text-yellow-800 border-yellow-200'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Benefit Removal Disputes</h3>
          <p className="text-sm text-gray-600 mt-1">
            Review and manage disputes for verified benefit removals
          </p>
        </div>
      </div>

      {/* Status Filter Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'pending', label: 'Pending', count: disputes.filter(d => d.status === 'pending').length },
            { key: 'approved', label: 'Approved', count: disputes.filter(d => d.status === 'approved').length },
            { key: 'rejected', label: 'Rejected', count: disputes.filter(d => d.status === 'rejected').length }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedStatus(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedStatus === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {selectedStatus === tab.key && disputes.length > 0 && (
                <span className="ml-2 bg-blue-100 text-blue-600 py-0.5 px-2 rounded-full text-xs">
                  {disputes.length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Disputes List */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading disputes...</p>
        </div>
      ) : disputes.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <AlertTriangle className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>No {selectedStatus} disputes found</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Benefit & Company
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Requested By
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {disputes.map((dispute) => (
                <tr key={dispute.dispute_id}>
                  {/* Benefit & Company */}
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      {dispute.benefit_icon && (
                        <span className="text-lg">{dispute.benefit_icon}</span>
                      )}
                      <div>
                        <div className="font-medium text-gray-900">
                          {dispute.benefit_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {dispute.company_name}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* Requested By */}
                  <td className="px-4 py-3">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {dispute.user_first_name} {dispute.user_last_name}
                      </div>
                      <div className="text-sm text-gray-500">{dispute.user_email}</div>
                      <div className="text-xs text-gray-400">
                        {formatDate(dispute.created_at)}
                      </div>
                    </div>
                  </td>

                  {/* Reason */}
                  <td className="px-4 py-3">
                    <div className="max-w-xs">
                      <p className="text-sm text-gray-900 truncate" title={dispute.reason}>
                        {dispute.reason}
                      </p>
                      {dispute.admin_comment && (
                        <p className="text-xs text-blue-600 mt-1 truncate" title={dispute.admin_comment}>
                          Admin: {dispute.admin_comment}
                        </p>
                      )}
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-4 py-3">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(dispute.status)}`}>
                      {getStatusIcon(dispute.status)}
                      {dispute.status}
                    </span>
                  </td>

                  {/* Progress */}
                  <td className="px-4 py-3">
                    <div className="text-sm">
                      <div className="flex items-center gap-2 mb-1">
                        <AlertTriangle className="w-3 h-3 text-yellow-600" />
                        <span className="font-medium">{dispute.approved_disputes_count}/2</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {dispute.pending_disputes_count} pending
                      </div>
                      {dispute.approved_disputes_count >= 2 && (
                        <div className="text-xs text-red-600 font-medium mt-1">
                          Auto-removal
                        </div>
                      )}
                    </div>
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-3">
                    {selectedStatus === 'pending' ? (
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => handleProcessDispute(dispute.dispute_id, 'approve')}
                          disabled={processingDispute === dispute.dispute_id}
                          className="bg-green-600 hover:bg-green-700 text-white"
                          title="Approve Dispute"
                        >
                          <CheckCircle className="w-3 h-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openCommentModal(dispute.dispute_id)}
                          disabled={processingDispute === dispute.dispute_id}
                          className="text-red-600 border-red-300 hover:bg-red-50"
                          title="Reject Dispute"
                        >
                          <XCircle className="w-3 h-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`/companies/${dispute.company_id}`, '_blank')}
                          className="text-blue-600 border-blue-300 hover:bg-blue-50"
                          title="View Company"
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                      </div>
                    ) : (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/companies/${dispute.company_id}`, '_blank')}
                        className="text-blue-600 border-blue-300 hover:bg-blue-50"
                        title="View Company"
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Comment Modal */}
      {showCommentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Reject Dispute
              </h3>
              <textarea
                value={adminComment}
                onChange={(e) => setAdminComment(e.target.value)}
                placeholder="Please provide a reason for rejecting this dispute..."
                className="w-full p-3 border border-gray-300 rounded-md resize-none"
                rows={4}
                maxLength={500}
              />
              <div className="flex items-center gap-3 mt-4">
                <Button
                  onClick={() => handleProcessDispute(showCommentModal, 'reject')}
                  disabled={!adminComment.trim() || processingDispute === showCommentModal}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {processingDispute === showCommentModal ? 'Processing...' : 'Reject Dispute'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCommentModal(null)
                    setAdminComment('')
                  }}
                  disabled={processingDispute === showCommentModal}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
