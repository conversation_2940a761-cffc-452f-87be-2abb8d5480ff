'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Building2,
  Users,
  Gift,
  BarChart3,
  CheckCircle,
  XCircle,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  AlertTriangle,
  Mail,
  Crown,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { AdminCompanyBenefits } from '@/components/admin-company-benefits'
import { AdminBenefitCategories } from '@/components/admin-benefit-categories'
import { AdminBenefitRemovalDisputes } from '@/components/admin-benefit-removal-disputes'
import { SafeDate } from '@/components/ui/safe-date'
import { Pagination } from '@/components/ui/pagination'

interface AdminStats {
  totalCompanies: number
  totalUsers: number
  totalBenefits: number
}

interface Activity {
  id: string
  event_type: 'company_added' | 'user_registered' | 'benefit_verified' | 'benefit_disputed'
  event_description: string
  user_id?: string
  user_email?: string
  user_name?: string
  company_id?: string
  company_name?: string
  benefit_id?: string
  benefit_name?: string
  metadata?: Record<string, any>
  created_at: string
}

interface Company {
  id: string
  name: string
  location: string
  industry: string
  size: string
  domain: string | null
  description: string | null
  career_url: string | null
  verified: boolean
  benefit_count: number
  verified_benefit_count: number
  total_user_count?: number
}

interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  company_name?: string
  company_assignment_type?: 'explicit' | 'domain' | null
  explicit_company_name?: string
  domain_company_name?: string
  company_association_date?: string
  last_login?: string
  payment_status?: string
  active_sessions: number
  created_at: string
}

interface Benefit {
  id: string
  name: string
  category: string
  icon?: string
  company_count: number
  verified_count: number
  unverified_count: number
}

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'companies' | 'users' | 'benefits' | 'benefit-categories' | 'company-benefits' | 'disputes'>('overview')
  const [stats, setStats] = useState<AdminStats>({
    totalCompanies: 0,
    totalUsers: 0,
    totalBenefits: 0
  })

  // Ref to track ongoing API calls and prevent duplicates
  const fetchingRef = useRef<Set<string>>(new Set())
  const [companies, setCompanies] = useState<Company[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [benefits, setBenefits] = useState<Benefit[]>([])
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(false)
  const [activitiesLoading, setActivitiesLoading] = useState(false)

  // Pagination state
  const [companiesPagination, setCompaniesPagination] = useState({ page: 1, limit: 20, total: 0, totalPages: 0 })
  const [usersPagination, setUsersPagination] = useState({ page: 1, limit: 20, total: 0, totalPages: 0 })
  const [benefitsPagination, setBenefitsPagination] = useState({ page: 1, limit: 50, total: 0, totalPages: 0 })
  const [companyBenefitsPagination, setCompanyBenefitsPagination] = useState({ page: 1, limit: 50, total: 0, totalPages: 0 })
  const [mounted, setMounted] = useState(false)
  const [showAddCompanyModal, setShowAddCompanyModal] = useState(false)
  const [showEditCompanyModal, setShowEditCompanyModal] = useState(false)
  const [editingCompany, setEditingCompany] = useState<Company | null>(null)
  const [showAddBenefitModal, setShowAddBenefitModal] = useState(false)
  const [showEditBenefitModal, setShowEditBenefitModal] = useState(false)
  const [editingBenefit, setEditingBenefit] = useState<Benefit | null>(null)
  const [showUserDetailModal, setShowUserDetailModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<any>(null)


  const [showCompanyBenefits, setShowCompanyBenefits] = useState<{companyId: string, companyName: string} | null>(null)
  const [companyBenefits, setCompanyBenefits] = useState<any[]>([])
  const [companyBenefitsFilter, setCompanyBenefitsFilter] = useState('all')
  const [companyBenefitsSearch, setCompanyBenefitsSearch] = useState('')

  // Search states for each section
  const [companiesSearch, setCompaniesSearch] = useState('')
  const [usersSearch, setUsersSearch] = useState('')
  const [benefitsSearch, setBenefitsSearch] = useState('')

  const [newCompany, setNewCompany] = useState({
    name: '',
    location: '',
    industry: '',
    size: 'medium',
    domain: '',
    description: '',
    career_url: ''
  })
  const [newBenefit, setNewBenefit] = useState({
    name: '',
    category: 'health',
    category_id: '',
    icon: ''
  })
  const [benefitCategories, setBenefitCategories] = useState<any[]>([])

  useEffect(() => {
    setMounted(true)
    fetchStats()
    fetchActivities()
    fetchBenefitCategories()
  }, [])

  // Single effect for tab changes (immediate load, non-search tabs)
  useEffect(() => {
    if (!mounted) return

    if (activeTab !== 'overview' && activeTab !== 'companies' && activeTab !== 'users' && activeTab !== 'benefits') {
      fetchTabData()
    }
  }, [activeTab, mounted])

  useEffect(() => {
    if (activeTab === 'company-benefits') {
      // Reset to page 1 when filter or search changes
      setCompanyBenefitsPagination(prev => ({ ...prev, page: 1 }))
      fetchCompanyBenefits(1)
    }
  }, [companyBenefitsFilter])

  // Debounced search effect
  useEffect(() => {
    if (activeTab === 'company-benefits') {
      const timeoutId = setTimeout(() => {
        setCompanyBenefitsPagination(prev => ({ ...prev, page: 1 }))
        fetchCompanyBenefits(1)
      }, 300) // 300ms debounce

      return () => clearTimeout(timeoutId)
    }
  }, [companyBenefitsSearch])

  const fetchStats = async () => {
    const fetchKey = 'admin-stats'

    // Prevent duplicate calls
    if (fetchingRef.current.has(fetchKey)) {
      return
    }

    fetchingRef.current.add(fetchKey)

    try {
      // Fetch basic stats from multiple endpoints
      const [companiesRes, usersRes, benefitsRes] = await Promise.all([
        fetch('/api/admin/companies?limit=1000'), // Fetch all companies for accurate verified count
        fetch('/api/admin/users?limit=1'),
        fetch('/api/admin/benefits?limit=1')
      ])

      const [companiesData, usersData, benefitsData] = await Promise.all([
        companiesRes.json(),
        usersRes.json(),
        benefitsRes.json()
      ])

      setStats({
        totalCompanies: companiesData.pagination?.total || 0,
        totalUsers: usersData.pagination?.total || 0,
        totalBenefits: benefitsData.pagination?.total || 0
      })
    } catch (error) {
      console.error('Error fetching admin stats:', error)
    } finally {
      fetchingRef.current.delete(fetchKey)
    }
  }

  const fetchBenefitCategories = async () => {
    const fetchKey = 'benefit-categories'

    // Prevent duplicate calls
    if (fetchingRef.current.has(fetchKey)) {
      return
    }

    fetchingRef.current.add(fetchKey)

    try {
      const response = await fetch('/api/benefit-categories')
      if (response.ok) {
        const data = await response.json()
        setBenefitCategories(data)
        // Set default category_id if categories are available
        if (data.length > 0 && !newBenefit.category_id) {
          setNewBenefit(prev => ({
            ...prev,
            category_id: data[0].id,
            category: data[0].name
          }))
        }
      }
    } catch (error) {
      console.error('Error fetching benefit categories:', error)
    } finally {
      fetchingRef.current.delete(fetchKey)
    }
  }

  const fetchActivities = async () => {
    const fetchKey = 'admin-activities'

    // Prevent duplicate calls
    if (fetchingRef.current.has(fetchKey)) {
      return
    }

    fetchingRef.current.add(fetchKey)

    try {
      setActivitiesLoading(true)
      const response = await fetch('/api/admin/activities?limit=10')
      const data = await response.json()

      if (response.ok) {
        setActivities(data.activities || [])
      } else {
        console.error('Error fetching activities:', data.error)
        setActivities([])
      }
    } catch (error) {
      console.error('Error fetching activities:', error)
      setActivities([])
    } finally {
      setActivitiesLoading(false)
      fetchingRef.current.delete(fetchKey)
    }
  }

  const getActivityColor = (eventType: string) => {
    switch (eventType) {
      case 'company_added':
        return 'bg-green-500'
      case 'user_registered':
        return 'bg-orange-500'
      case 'benefit_verified':
        return 'bg-blue-500'
      case 'benefit_disputed':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`

    return date.toLocaleDateString()
  }

  const fetchTabData = async (page?: number) => {
    const currentPage = page || 1
    const fetchKey = `${activeTab}-${currentPage}-${companiesSearch}-${usersSearch}-${benefitsSearch}`

    // Prevent duplicate calls
    if (fetchingRef.current.has(fetchKey)) {
      return
    }

    fetchingRef.current.add(fetchKey)
    setLoading(true)

    try {
      if (activeTab === 'companies') {
        const searchParam = companiesSearch ? `&search=${encodeURIComponent(companiesSearch)}` : ''
        const response = await fetch(`/api/admin/companies?limit=${companiesPagination.limit}&page=${currentPage}&sort=name${searchParam}`)
        const data = await response.json()
        setCompanies(data.companies || [])
        setCompaniesPagination(prev => ({ ...prev, ...data.pagination, page: currentPage }))
      } else if (activeTab === 'users') {
        const searchParam = usersSearch ? `&search=${encodeURIComponent(usersSearch)}` : ''
        const response = await fetch(`/api/admin/users?limit=${usersPagination.limit}&page=${currentPage}&sort=email${searchParam}`)
        const data = await response.json()
        setUsers(data.users || [])
        setUsersPagination(prev => ({ ...prev, ...data.pagination, page: currentPage }))
      } else if (activeTab === 'benefits') {
        const searchParam = benefitsSearch ? `&search=${encodeURIComponent(benefitsSearch)}` : ''
        const response = await fetch(`/api/admin/benefits?limit=${benefitsPagination.limit}&page=${currentPage}&sort=name${searchParam}`)
        const data = await response.json()
        setBenefits(data.benefits || [])
        setBenefitsPagination(prev => ({ ...prev, ...data.pagination, page: currentPage }))
      } else if (activeTab === 'disputes') {
        // Disputes are now handled by the AdminBenefitRemovalDisputes component
      } else if (activeTab === 'company-benefits') {
        await fetchCompanyBenefits(page)
      }
    } catch (error) {
      console.error('Error fetching tab data:', error)
    } finally {
      setLoading(false)
      fetchingRef.current.delete(fetchKey)
    }
  }

  // Search handlers
  const handleCompaniesSearch = (searchTerm: string) => {
    setCompaniesSearch(searchTerm)
    setCompaniesPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleUsersSearch = (searchTerm: string) => {
    setUsersSearch(searchTerm)
    setUsersPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleBenefitsSearch = (searchTerm: string) => {
    setBenefitsSearch(searchTerm)
    setBenefitsPagination(prev => ({ ...prev, page: 1 }))
  }

  // Debounced search effect for companies, users, and benefits tabs
  useEffect(() => {
    if (!mounted) return

    const timeoutId = setTimeout(() => {
      if (activeTab === 'companies' || activeTab === 'users' || activeTab === 'benefits') {
        fetchTabData(1)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [companiesSearch, usersSearch, benefitsSearch, mounted])

  // Separate effect for initial load of searchable tabs when tab changes
  useEffect(() => {
    if (!mounted) return

    if (activeTab === 'companies' || activeTab === 'users' || activeTab === 'benefits') {
      fetchTabData()
    }
  }, [activeTab, mounted])



  const fetchCompanyBenefits = async (page?: number) => {
    const currentPage = page || companyBenefitsPagination.page
    const fetchKey = `company-benefits-${currentPage}-${companyBenefitsFilter}-${companyBenefitsSearch}`

    // Prevent duplicate calls
    if (fetchingRef.current.has(fetchKey)) {
      return
    }

    fetchingRef.current.add(fetchKey)

    try {
      let url = `/api/admin/company-benefits?limit=${companyBenefitsPagination.limit}&page=${currentPage}`

      if (companyBenefitsFilter === 'verified') {
        url += '&verified=true'
      } else if (companyBenefitsFilter === 'unverified') {
        url += '&verified=false'
      } else if (companyBenefitsFilter === 'disputed') {
        url += '&hasDisputes=true'
      }

      if (companyBenefitsSearch.trim()) {
        url += `&search=${encodeURIComponent(companyBenefitsSearch.trim())}`
      }

      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data.companyBenefits || [])
        setCompanyBenefitsPagination(prev => ({ ...prev, ...data.pagination, page: currentPage }))
      } else {
        console.error('Failed to fetch company benefits')
        setCompanyBenefits([])
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
      setCompanyBenefits([])
    } finally {
      fetchingRef.current.delete(fetchKey)
    }
  }

  const handleAddCompany = async () => {
    try {
      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCompany)
      })

      if (response.ok) {
        setShowAddCompanyModal(false)
        setNewCompany({
          name: '',
          location: '',
          industry: '',
          size: 'medium',
          domain: '',
          description: '',
          career_url: ''
        })
        if (activeTab === 'companies') {
          fetchTabData()
        }
        fetchStats()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to add company')
      }
    } catch (error) {
      console.error('Error adding company:', error)
      alert('Failed to add company')
    }
  }

  const handleAddBenefit = async () => {
    try {
      const response = await fetch('/api/admin/benefits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newBenefit)
      })

      if (response.ok) {
        setShowAddBenefitModal(false)
        setNewBenefit({
          name: '',
          category: 'health',
          category_id: '',
          icon: ''
        })
        if (activeTab === 'benefits') {
          fetchTabData()
        }
        fetchStats()
      } else {
        const error = await response.json()
        const errorMessage = error.error || 'Failed to add benefit'

        // Provide more specific error messages
        if (errorMessage.includes('duplicate key') || errorMessage.includes('already exists')) {
          alert(`A benefit with the name "${newBenefit.name}" already exists. Please choose a different name.`)
        } else {
          alert(errorMessage)
        }
      }
    } catch (error) {
      console.error('Error adding benefit:', error)
      alert('Failed to add benefit')
    }
  }

  const handleEditBenefit = (benefit: Benefit) => {
    setEditingBenefit(benefit)
    setShowEditBenefitModal(true)
  }

  const handleUpdateBenefit = async () => {
    if (!editingBenefit) return

    try {
      const response = await fetch('/api/admin/benefits', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          benefitId: editingBenefit.id,
          data: {
            name: editingBenefit.name,
            category: editingBenefit.category,
            icon: editingBenefit.icon
          }
        })
      })

      if (response.ok) {
        setShowEditBenefitModal(false)
        setEditingBenefit(null)
        if (activeTab === 'benefits') {
          fetchTabData()
        }
        fetchStats()
      } else {
        const error = await response.json()
        const errorMessage = error.error || 'Failed to update benefit'

        // Provide more specific error messages
        if (errorMessage.includes('duplicate key') || errorMessage.includes('already exists')) {
          alert(`A benefit with the name "${editingBenefit.name}" already exists. Please choose a different name.`)
        } else {
          alert(errorMessage)
        }
      }
    } catch (error) {
      console.error('Error updating benefit:', error)
      alert('Failed to update benefit')
    }
  }

  const handleDeleteBenefit = async (benefitId: string, benefitName: string) => {
    if (!confirm(`Are you sure you want to delete "${benefitName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/benefits?benefitId=${benefitId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        if (activeTab === 'benefits') {
          fetchTabData()
        }
        fetchStats()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete benefit')
      }
    } catch (error) {
      console.error('Error deleting benefit:', error)
      alert('Failed to delete benefit')
    }
  }


  const handleEditCompany = (company: Company) => {
    setEditingCompany(company)
    setShowEditCompanyModal(true)
  }

  const handleUpdateCompany = async () => {
    if (!editingCompany) return

    try {
      const response = await fetch('/api/admin/companies', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyId: editingCompany.id,
          action: 'update',
          data: {
            name: editingCompany.name,
            location: editingCompany.location,
            industry: editingCompany.industry,
            size: editingCompany.size,
            domain: editingCompany.domain,
            description: editingCompany.description,
            career_url: editingCompany.career_url
          }
        })
      })

      if (response.ok) {
        setShowEditCompanyModal(false)
        setEditingCompany(null)
        fetchTabData()
        fetchStats()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update company')
      }
    } catch (error) {
      console.error('Error updating company:', error)
      alert('Failed to update company')
    }
  }

  const handleDeleteCompany = async (companyId: string, companyName: string) => {
    if (!confirm(`Are you sure you want to delete "${companyName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/companies?companyId=${companyId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchTabData()
        fetchStats()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete company')
      }
    } catch (error) {
      console.error('Error deleting company:', error)
      alert('Failed to delete company')
    }
  }

  const handleViewUser = async (user: any) => {
    try {
      // Fetch detailed user information
      const response = await fetch(`/api/admin/users/${user.id}`)
      if (response.ok) {
        const detailedUser = await response.json()
        setSelectedUser(detailedUser)
        setShowUserDetailModal(true)
      } else {
        alert('Failed to load user details')
      }
    } catch (error) {
      console.error('Error fetching user details:', error)
      alert('Failed to load user details')
    }
  }

  const handleDeleteUser = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to delete user "${userEmail}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/users?userId=${userId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchTabData()
        fetchStats()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete user')
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      alert('Failed to delete user')
    }
  }

  const handleTogglePaymentStatus = async (userId: string, userEmail: string, currentStatus: string) => {
    // Toggle between payment statuses: free -> paying -> free
    const newStatus = currentStatus === 'free' ? 'paying' : 'free'

    if (!confirm(`Change payment status for "${userEmail}" from "${currentStatus}" to "${newStatus}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}/payment-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          payment_status: newStatus
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`Payment status updated successfully: ${result.message}`)
        fetchTabData() // Refresh the user list
        fetchStats() // Refresh stats if needed
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update payment status')
      }
    } catch (error) {
      console.error('Error updating payment status:', error)
      alert('Failed to update payment status')
    }
  }

  const handleDiscoverUsers = async (companyId: string, companyName: string) => {
    if (!confirm(`Discover and notify existing users for "${companyName}"? This will send emails to users with matching email domains.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/companies/${companyId}/discover-users`, {
        method: 'POST'
      })

      if (response.ok) {
        const result = await response.json()
        const discovery = result.result

        let message = `User discovery completed for ${companyName}:\n\n`
        message += `• Found ${discovery.discoveredUsers.length} existing users\n`
        message += `• Sent ${discovery.notificationsSent} notification emails\n`

        if (discovery.errors.length > 0) {
          message += `• ${discovery.errors.length} errors occurred\n`
        }

        if (discovery.discoveredUsers.length > 0) {
          message += `\nNotified users:\n${discovery.discoveredUsers.join('\n')}`
        }

        alert(message)
        fetchTabData() // Refresh to show updated user counts
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to discover users')
      }
    } catch (error) {
      console.error('Error discovering users:', error)
      alert('Failed to discover users')
    }
  }









  const handleExportUsers = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=1000')
      const data = await response.json()

      if (response.ok) {
        const csvHeaders = 'Name,Email,Company,Assignment Type,Sessions,Joined\n'
        const csvRows = data.users.map((user: User) => {
          const joinedDate = user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'
          const assignmentType = user.company_assignment_type === 'explicit' ? 'Assigned' :
                                user.company_assignment_type === 'domain' ? 'Domain Match' : 'None'
          return `"${user.first_name} ${user.last_name}","${user.email}","${user.company_name || 'N/A'}","${assignmentType}","${user.active_sessions}","${joinedDate}"`
        }).join('\n')

        const csvContent = csvHeaders + csvRows
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        const today = new Date()
        a.download = `workwell_users_${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error exporting users:', error)
      alert('Failed to export users')
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'companies', label: 'Companies', icon: Building2 },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'benefits', label: 'Benefits', icon: Gift },
    { id: 'benefit-categories', label: 'Categories', icon: Settings },
    { id: 'company-benefits', label: 'Company Benefits', icon: Building2 },
    { id: 'disputes', label: 'Removal Disputes', icon: AlertTriangle },
  ] as const

  return (
    <div className="space-y-6">
      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-sm border">
        {/* Mobile-responsive tab navigation */}
        <div className="overflow-x-auto">
          <div className="flex space-x-1 p-1 min-w-max">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">{tab.label}</span>
                  <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building2 className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Companies</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCompanies}</p>
                </div>
              </div>
            </div>



            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Gift className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Benefits</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalBenefits}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={() => setActiveTab('companies')}
                variant="outline"
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">Manage Companies</div>
                  <div className="text-sm text-gray-500">Approve, verify, and edit company profiles</div>
                </div>
              </Button>
              
              <Button
                onClick={() => setActiveTab('benefits')}
                variant="outline"
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">Manage Benefits</div>
                  <div className="text-sm text-gray-500">Add new benefit types and categories</div>
                </div>
              </Button>
              
              <Button
                onClick={() => setActiveTab('users')}
                variant="outline"
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">User Management</div>
                  <div className="text-sm text-gray-500">View and manage user accounts</div>
                </div>
              </Button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              {activitiesLoading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading activities...</span>
                </div>
              ) : activities.length > 0 ? (
                activities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 text-sm">
                    <div className={`w-2 h-2 ${getActivityColor(activity.event_type)} rounded-full`}></div>
                    <span className="text-gray-600 flex-1">{activity.event_description}</span>
                    <span className="text-gray-400">{formatTimeAgo(activity.created_at)}</span>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  No recent activities found
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Companies Tab */}
      {activeTab === 'companies' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Company Management</h3>
              <p className="text-sm text-gray-600 mt-1">
                Manage companies and view mapped user accounts. The &ldquo;Users&rdquo; column shows how many app users are linked to each company.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search companies..."
                  value={companiesSearch}
                  onChange={(e) => handleCompaniesSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-64"
                />
              </div>
              <Button
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setShowAddCompanyModal(true)}
              >
                <Plus className="w-4 h-4" />
                Add Company
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading companies...</p>
            </div>
          ) : companies.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domain</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Benefits</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {companies.map((company) => (
                    <tr key={company.id}>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Building2 className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              {company.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {company.domain ? (
                          <span className="font-mono text-blue-600">{company.domain}</span>
                        ) : (
                          <span className="text-gray-400 italic">No domain</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">{company.location}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">{company.industry}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {company.verified_benefit_count}/{company.benefit_count}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Users className="w-3 h-3 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            {company.total_user_count || 0}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/companies/${company.id}`, '_blank')}
                            title="View Company"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditCompany(company)}
                            title="Edit Company"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowCompanyBenefits({companyId: company.id, companyName: company.name})}
                            className="text-purple-600 hover:text-purple-700"
                            title="Manage Company Benefits"
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                          {company.domain && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDiscoverUsers(company.id, company.name)}
                              className="text-blue-600 hover:text-blue-700"
                              title="Discover and notify existing users with matching email domain"
                            >
                              <Mail className="w-3 h-3" />
                            </Button>
                          )}

                          <Button
                            size="sm"
                            onClick={() => handleDeleteCompany(company.id, company.name)}
                            className="bg-red-600 hover:bg-red-700 text-white"
                            title="Delete Company"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Building2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No companies found</p>
            </div>
          )}

          {/* Companies Pagination */}
          <Pagination
            currentPage={companiesPagination.page}
            totalPages={companiesPagination.totalPages}
            totalItems={companiesPagination.total}
            itemsPerPage={companiesPagination.limit}
            itemName="companies"
            onPageChange={(page) => fetchTabData(page)}
          />
        </div>
      )}

      {/* Users Tab */}
      {activeTab === 'users' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">User Management</h3>
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={usersSearch}
                  onChange={(e) => handleUsersSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-64"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportUsers}
              >
                Export Users
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading users...</p>
            </div>
          ) : users.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <Users className="w-4 h-4 text-purple-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              {user.first_name} {user.last_name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">{user.email}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {user.company_name ? (
                          <div className="flex flex-col space-y-1">
                            <div className="flex items-center space-x-1">
                              <span>{user.company_name}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                                user.company_assignment_type === 'explicit'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {user.company_assignment_type === 'explicit' ? 'Assigned' : 'Domain Match'}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">No company</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            user.payment_status === 'paying'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {user.payment_status === 'paying' && <Crown className="w-3 h-3 mr-1" />}
                            {user.payment_status === 'paying' ? 'Premium' : 'Free'}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTogglePaymentStatus(user.id, user.email, user.payment_status || 'free')}
                            className="text-xs px-2 py-1 h-6"
                            title="Toggle Payment Status"
                          >
                            Toggle
                          </Button>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">{user.active_sessions}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <SafeDate date={user.created_at} />
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewUser(user)}
                            title="View User Details"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>





                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteUser(user.id, user.email)}
                            title="Delete User"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No users found</p>
            </div>
          )}

          {/* Users Pagination */}
          <Pagination
            currentPage={usersPagination.page}
            totalPages={usersPagination.totalPages}
            totalItems={usersPagination.total}
            itemsPerPage={usersPagination.limit}
            itemName="users"
            onPageChange={(page) => fetchTabData(page)}
          />
        </div>
      )}

      {/* Benefit Categories Tab */}
      {activeTab === 'benefit-categories' && (
        <AdminBenefitCategories />
      )}

      {/* Benefits Tab */}
      {activeTab === 'benefits' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Benefits Management</h3>
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search benefits..."
                  value={benefitsSearch}
                  onChange={(e) => handleBenefitsSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-64"
                />
              </div>
              <Button
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setShowAddBenefitModal(true)}
              >
                <Plus className="w-4 h-4" />
                Add Benefit
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading benefits...</p>
            </div>
          ) : benefits.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Benefit
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Companies
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Verified
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pending
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {benefits.map((benefit) => (
                    <tr key={benefit.id}>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-3">
                          {benefit.icon && (
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <span className="text-lg">{benefit.icon}</span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">{benefit.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                          {benefit.category.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <span className="font-medium text-gray-900">{benefit.company_count}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <span className="font-medium text-green-600">{benefit.verified_count}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <span className="font-medium text-yellow-600">{benefit.unverified_count}</span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditBenefit(benefit)}
                            title="Edit benefit"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            className="bg-red-600 hover:bg-red-700 text-white"
                            onClick={() => handleDeleteBenefit(benefit.id, benefit.name)}
                            title="Delete benefit"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Gift className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No benefits found</p>
            </div>
          )}

          {/* Benefits Pagination */}
          <Pagination
            currentPage={benefitsPagination.page}
            totalPages={benefitsPagination.totalPages}
            totalItems={benefitsPagination.total}
            itemsPerPage={benefitsPagination.limit}
            itemName="benefits"
            onPageChange={(page) => fetchTabData(page)}
          />
        </div>
      )}

      {/* Company Benefits Tab */}
      {activeTab === 'company-benefits' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0 mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Company Benefits Management</h3>
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search companies..."
                  value={companyBenefitsSearch}
                  onChange={(e) => setCompanyBenefitsSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-full sm:w-64"
                />
              </div>
              <select
                value={companyBenefitsFilter}
                onChange={(e) => setCompanyBenefitsFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-full sm:w-auto"
              >
                <option value="all">All Benefits</option>
                <option value="verified">Verified Only</option>
                <option value="unverified">Unverified Only</option>
                <option value="disputed">With Disputes</option>
              </select>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading company benefits...</p>
            </div>
          ) : companyBenefits.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No company benefits found.</p>
            </div>
          ) : (
            <div>
              {/* Desktop table view */}
              <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Company
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Benefit
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Verification Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User Verifications
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Added Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {companyBenefits.map((companyBenefit) => (
                    <tr key={companyBenefit.company_benefit_id}>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-2">
                          <div>
                            <div className="font-medium text-gray-900">{companyBenefit.company_name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-2">
                          {companyBenefit.benefit_icon && (
                            <span className="text-lg">{companyBenefit.benefit_icon}</span>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">{companyBenefit.benefit_name}</div>
                            <div className="text-sm text-gray-500">
                              {companyBenefit.benefit_category.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          companyBenefit.is_verified
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {companyBenefit.is_verified ? (
                            <>
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Admin Verified
                            </>
                          ) : (
                            <>
                              <XCircle className="w-3 h-3 mr-1" />
                              Unverified
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-2 text-sm">
                          <span className="text-green-600 font-medium">
                            {companyBenefit.confirmed_count} confirmed
                          </span>
                          <span className="text-gray-400">/</span>
                          <span className={`font-medium ${
                            companyBenefit.disputed_count > 0 ? 'text-red-600' : 'text-gray-400'
                          }`}>
                            {companyBenefit.disputed_count} disputed
                          </span>
                          {companyBenefit.has_disputes && (
                            <div title="Has disputes">
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        <SafeDate
                          date={companyBenefit.created_at}
                          fallback="Loading..."
                        />
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setShowCompanyBenefits({
                              companyId: companyBenefit.company_id,
                              companyName: companyBenefit.company_name
                            })}
                            className="text-purple-600 hover:text-purple-700"
                            title="Manage All Benefits for This Company"
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              if (mounted) {
                                window.open(`/companies/${companyBenefit.company_id}`, '_blank')
                              }
                            }}
                            title="View Company Page"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile card view */}
            <div className="lg:hidden space-y-4">
              {companyBenefits.map((companyBenefit) => (
                <div key={companyBenefit.company_benefit_id} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 truncate">{companyBenefit.company_name}</h4>
                      <div className="flex items-center space-x-2 mt-1">
                        {companyBenefit.benefit_icon && (
                          <span className="text-sm">{companyBenefit.benefit_icon}</span>
                        )}
                        <span className="font-medium text-gray-900 text-sm">{companyBenefit.benefit_name}</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {companyBenefit.benefit_category.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ml-2 ${
                      companyBenefit.is_verified
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {companyBenefit.is_verified ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Verified
                        </>
                      ) : (
                        <>
                          <XCircle className="w-3 h-3 mr-1" />
                          Unverified
                        </>
                      )}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <p className="text-gray-500">User Verifications</p>
                      <div className="flex items-center space-x-1">
                        <span className="text-green-600 font-medium">
                          {companyBenefit.confirmed_count} confirmed
                        </span>
                        <span className="text-gray-400">/</span>
                        <span className={`font-medium ${
                          companyBenefit.disputed_count > 0 ? 'text-red-600' : 'text-gray-400'
                        }`}>
                          {companyBenefit.disputed_count} disputed
                        </span>
                        {companyBenefit.has_disputes && (
                          <AlertTriangle className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500">Added Date</p>
                      <p className="font-medium text-gray-900">
                        <SafeDate
                          date={companyBenefit.created_at}
                          fallback="Loading..."
                        />
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowCompanyBenefits({
                        companyId: companyBenefit.company_id,
                        companyName: companyBenefit.company_name
                      })}
                      className="text-purple-600 hover:text-purple-700 flex-1"
                      title="Manage All Benefits for This Company"
                    >
                      <Settings className="w-3 h-3 mr-1" />
                      Manage
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        if (mounted) {
                          window.open(`/companies/${companyBenefit.company_id}`, '_blank')
                        }
                      }}
                      title="View Company Page"
                      className="flex-1"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
              </div>
            </div>
          )}

          {/* Company Benefits Pagination */}
          <Pagination
            currentPage={companyBenefitsPagination.page}
            totalPages={companyBenefitsPagination.totalPages}
            totalItems={companyBenefitsPagination.total}
            itemsPerPage={companyBenefitsPagination.limit}
            itemName="company benefits"
            onPageChange={(page) => fetchCompanyBenefits(page)}
          />
        </div>
      )}

      {/* Benefit Removal Disputes Tab */}
      {activeTab === 'disputes' && <AdminBenefitRemovalDisputes />}

      {/* Add Company Modal */}
      {showAddCompanyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Company</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                <input
                  type="text"
                  value={newCompany.name}
                  onChange={(e) => setNewCompany({...newCompany, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter company name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input
                  type="text"
                  value={newCompany.location}
                  onChange={(e) => setNewCompany({...newCompany, location: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter location"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                <input
                  type="text"
                  value={newCompany.industry}
                  onChange={(e) => setNewCompany({...newCompany, industry: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter industry"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Size</label>
                <select
                  value={newCompany.size}
                  onChange={(e) => setNewCompany({...newCompany, size: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                >
                  <option value="startup">Startup</option>
                  <option value="small">Small (1-50)</option>
                  <option value="medium">Medium (51-200)</option>
                  <option value="large">Large (201-1000)</option>
                  <option value="enterprise">Enterprise (1000+)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Domain</label>
                <input
                  type="text"
                  value={newCompany.domain}
                  onChange={(e) => setNewCompany({...newCompany, domain: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="company.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={newCompany.description}
                  onChange={(e) => setNewCompany({...newCompany, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter company description"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Career URL</label>
                <input
                  type="url"
                  value={newCompany.career_url}
                  onChange={(e) => setNewCompany({...newCompany, career_url: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="https://company.com/careers"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button onClick={handleAddCompany} className="flex-1">
                Add Company
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowAddCompanyModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Company Modal */}
      {showEditCompanyModal && editingCompany && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Edit Company</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                <input
                  type="text"
                  value={editingCompany.name}
                  onChange={(e) => setEditingCompany({...editingCompany, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter company name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input
                  type="text"
                  value={editingCompany.location}
                  onChange={(e) => setEditingCompany({...editingCompany, location: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter location"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                <input
                  type="text"
                  value={editingCompany.industry}
                  onChange={(e) => setEditingCompany({...editingCompany, industry: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter industry"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Size</label>
                <select
                  value={editingCompany.size}
                  onChange={(e) => setEditingCompany({...editingCompany, size: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                >
                  <option value="startup">Startup</option>
                  <option value="small">Small (1-50)</option>
                  <option value="medium">Medium (51-200)</option>
                  <option value="large">Large (201-1000)</option>
                  <option value="enterprise">Enterprise (1000+)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email Domain</label>
                <input
                  type="text"
                  value={editingCompany.domain || ''}
                  onChange={(e) => setEditingCompany({...editingCompany, domain: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="company.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={editingCompany.description || ''}
                  onChange={(e) => setEditingCompany({...editingCompany, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter company description"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Career URL</label>
                <input
                  type="url"
                  value={editingCompany.career_url || ''}
                  onChange={(e) => setEditingCompany({...editingCompany, career_url: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="https://company.com/careers"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button onClick={handleUpdateCompany} className="flex-1">
                Update Company
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditCompanyModal(false)
                  setEditingCompany(null)
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Add Benefit Modal */}
      {showAddBenefitModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Benefit</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Benefit Name</label>
                <input
                  type="text"
                  value={newBenefit.name}
                  onChange={(e) => setNewBenefit({...newBenefit, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter benefit name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={newBenefit.category_id}
                  onChange={(e) => {
                    const selectedCategory = benefitCategories.find(cat => cat.id === e.target.value)
                    setNewBenefit({
                      ...newBenefit,
                      category_id: e.target.value,
                      category: selectedCategory?.name || ''
                    })
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                >
                  {benefitCategories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon && `${category.icon} `}{category.display_name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Icon (Emoji)</label>
                <input
                  type="text"
                  value={newBenefit.icon}
                  onChange={(e) => setNewBenefit({...newBenefit, icon: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="🏥"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button onClick={handleAddBenefit} className="flex-1">
                Add Benefit
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowAddBenefitModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Benefit Modal */}
      {showEditBenefitModal && editingBenefit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Edit Benefit</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Benefit Name</label>
                <input
                  type="text"
                  value={editingBenefit.name}
                  onChange={(e) => setEditingBenefit({...editingBenefit, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="Enter benefit name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={editingBenefit.category}
                  onChange={(e) => setEditingBenefit({...editingBenefit, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                >
                  <option value="health">Health</option>
                  <option value="time_off">Time Off</option>
                  <option value="financial">Financial</option>
                  <option value="development">Development</option>
                  <option value="wellness">Wellness</option>
                  <option value="work_life">Work Life</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Icon (Emoji)</label>
                <input
                  type="text"
                  value={editingBenefit.icon || ''}
                  onChange={(e) => setEditingBenefit({...editingBenefit, icon: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="🏥"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button onClick={handleUpdateBenefit} className="flex-1">
                Update Benefit
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditBenefitModal(false)
                  setEditingBenefit(null)
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* User Detail Modal */}
      {showUserDetailModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">User Details</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setShowUserDetailModal(false)
                  setSelectedUser(null)
                }}
              >
                ✕
              </Button>
            </div>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <p className="text-gray-900">{selectedUser.first_name} {selectedUser.last_name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <p className="text-gray-900">{selectedUser.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                  <p className="text-gray-600 font-mono text-sm">{selectedUser.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Joined</label>
                  <p className="text-gray-900"><SafeDate date={selectedUser.created_at} /></p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <p className="text-gray-900">{selectedUser.role}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      selectedUser.payment_status === 'paying'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedUser.payment_status === 'paying' && <Crown className="w-4 h-4 mr-2" />}
                      {selectedUser.payment_status === 'paying' ? 'Premium' : 'Free'}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTogglePaymentStatus(selectedUser.id, selectedUser.email, selectedUser.payment_status)}
                      className="text-sm"
                    >
                      Change Status
                    </Button>
                  </div>
                </div>
              </div>

              {/* Company Information */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3">Company Association</h4>
                {selectedUser.company_name ? (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <p className="font-medium text-gray-900">{selectedUser.company_name}</p>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            selectedUser.company_assignment_type === 'explicit'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {selectedUser.company_assignment_type === 'explicit' ? 'Explicitly Assigned' : 'Email Domain Match'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">Domain: {selectedUser.company_domain}</p>
                        <p className="text-sm text-gray-600">Company ID: {selectedUser.company_id}</p>
                        {selectedUser.explicit_company_name && selectedUser.domain_company_name &&
                         selectedUser.explicit_company_name !== selectedUser.domain_company_name && (
                          <div className="mt-2 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                            <p className="text-sm text-blue-800">
                              <strong>Note:</strong> User has both explicit assignment ({selectedUser.explicit_company_name})
                              and domain match ({selectedUser.domain_company_name}). Explicit assignment takes precedence.
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col space-y-2">


                      </div>
                    </div>
                    {selectedUser.company_association_date && (
                      <p className="text-sm text-gray-600">
                        Associated: <SafeDate date={selectedUser.company_association_date} />
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No company association</p>
                )}
              </div>

              {/* Session Information */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3">Session Activity</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Active Sessions</p>
                      <p className="text-lg font-semibold text-gray-900">{selectedUser.active_sessions || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Total Sessions</p>
                      <p className="text-lg font-semibold text-gray-900">{selectedUser.total_sessions || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Last Login</p>
                      <p className="text-gray-900">
                        {selectedUser.last_login
                          ? <SafeDate date={selectedUser.last_login} format="datetime" />
                          : 'No recent activity'
                        }
                      </p>
                    </div>
                  </div>

                  {selectedUser.sessions && selectedUser.sessions.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">Recent Sessions</p>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {selectedUser.sessions.slice(0, 5).map((session: any, index: number) => (
                          <div key={session.id} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">
                              <SafeDate date={session.created_at} format="datetime" />
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              session.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {session.is_active ? 'Active' : 'Expired'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Additional Details */}
              {selectedUser.additional_info && (
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-3">Additional Information</h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(selectedUser.additional_info, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowUserDetailModal(false)
                  setSelectedUser(null)
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Company Benefits Management Modal */}
      {showCompanyBenefits && (
        <AdminCompanyBenefits
          companyId={showCompanyBenefits.companyId}
          companyName={showCompanyBenefits.companyName}
          onClose={() => setShowCompanyBenefits(null)}
        />
      )}

    </div>
  )
}
