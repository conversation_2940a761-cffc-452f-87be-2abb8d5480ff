-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Companies table
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    size VARCHAR(50) CHECK (size IN ('startup', 'small', 'medium', 'large', 'enterprise')) NOT NULL,
    industry VARCHAR(255) NOT NULL,
    description TEXT,
    domain VARCHAR(255),
    career_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefits table
CREATE TABLE benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    category VARCHAR(50) CHECK (category IN ('health', 'time_off', 'financial', 'development', 'wellness', 'work_life', 'other')) NOT NULL,
    icon VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company benefits junction table
CREATE TABLE company_benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    added_by VARCHAR(255), -- User ID from auth system
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, benefit_id)
);

-- Company users table (for company representatives)
CREATE TABLE company_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, email)
);

-- Benefit verifications table
CREATE TABLE benefit_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    status VARCHAR(50) CHECK (status IN ('confirmed', 'disputed')) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Saved companies table (for user bookmarks)
CREATE TABLE saved_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, company_id)
);

-- Benefit removal disputes table
CREATE TABLE benefit_removal_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    reason TEXT NOT NULL, -- Reason for requesting removal
    status VARCHAR(50) CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
    admin_user_id VARCHAR(255), -- Admin who approved/rejected
    admin_comment TEXT, -- Admin's comment on the decision
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure one dispute per user per benefit
    UNIQUE(company_benefit_id, user_id)
);

-- Future: Job postings table (ready for implementation)
CREATE TABLE job_postings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    salary_min INTEGER,
    salary_max INTEGER,
    employment_type VARCHAR(50) CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'internship')) NOT NULL,
    remote_policy VARCHAR(50) CHECK (remote_policy IN ('on_site', 'remote', 'hybrid')) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_companies_location ON companies(location);
CREATE INDEX idx_companies_size ON companies(size);
CREATE INDEX idx_companies_industry ON companies(industry);
CREATE INDEX idx_companies_domain ON companies(domain);

CREATE INDEX idx_benefits_category ON benefits(category);
CREATE INDEX idx_benefits_name ON benefits(name);

CREATE INDEX idx_company_benefits_company_id ON company_benefits(company_id);
CREATE INDEX idx_company_benefits_benefit_id ON company_benefits(benefit_id);
CREATE INDEX idx_company_benefits_verified ON company_benefits(is_verified);

CREATE INDEX idx_company_users_company_id ON company_users(company_id);
CREATE INDEX idx_company_users_email ON company_users(email);

CREATE INDEX idx_benefit_verifications_company_benefit_id ON benefit_verifications(company_benefit_id);
CREATE INDEX idx_benefit_verifications_user_id ON benefit_verifications(user_id);
CREATE INDEX idx_benefit_verifications_status ON benefit_verifications(status);

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON benefit_removal_disputes(company_benefit_id);
CREATE INDEX idx_benefit_removal_disputes_user_id ON benefit_removal_disputes(user_id);
CREATE INDEX idx_benefit_removal_disputes_status ON benefit_removal_disputes(status);
CREATE INDEX idx_benefit_removal_disputes_created_at ON benefit_removal_disputes(created_at);

CREATE INDEX idx_job_postings_company_id ON job_postings(company_id);
CREATE INDEX idx_job_postings_location ON job_postings(location);
CREATE INDEX idx_job_postings_employment_type ON job_postings(employment_type);
CREATE INDEX idx_job_postings_remote_policy ON job_postings(remote_policy);
CREATE INDEX idx_job_postings_active ON job_postings(is_active);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_postings_updated_at BEFORE UPDATE ON job_postings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_removal_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_postings ENABLE ROW LEVEL SECURITY;

-- Public read access for companies and benefits
CREATE POLICY "Public companies are viewable by everyone" ON companies
    FOR SELECT USING (true);

CREATE POLICY "Public benefits are viewable by everyone" ON benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company benefits are viewable by everyone" ON company_benefits
    FOR SELECT USING (true);

CREATE POLICY "Public job postings are viewable by everyone" ON job_postings
    FOR SELECT USING (is_active = true);

-- Benefit removal disputes are viewable by admins and the users who created them
CREATE POLICY "Benefit removal disputes are viewable by admins" ON benefit_removal_disputes
    FOR SELECT USING (true); -- Will be restricted by application logic
